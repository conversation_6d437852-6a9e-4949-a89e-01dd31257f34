﻿<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:ccts="urn:oasis:names:specification:ubl:schema:xsd:CoreComponentParameters-2" xmlns:sdt="urn:oasis:names:specification:ubl:schema:xsd:SpecializedDatatypes-2" xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 UBL-Invoice-2.0.xsd">

<!--
******************************************************************************************************************

		OIOUBL Instance Documentation	

		title= ADVORD_03_03_00_Invoice_v2p1.xml	
		replaces= ADVORD_03_03_00_Invoice_v2p1.xml	
		publisher= "IT og Telestyrelsen"
		Creator= "Finn Christensen"
		created= 2006-09-08
		modified= 2007-03-26
		issued= 2007-03-26
		conformsTo= OIOUBL_ScenarioPackage_ADVORD
		description= "This document is produced as part of the OIOUBL Advanced Ordering procurement scenario package"
		rights= "It can be used following the Common Creative Licence"
		
		all terms derived from http://dublincore.org/documents/dcmi-terms/

		For more information, see www.oioubl.dk	<NAME_EMAIL>
		
******************************************************************************************************************
-->
<!-- The following TestInstance process instruction is for customization usage. It indicates that the instance is for testing purposes -->
<?TestInstance 
	ResponseTo="smtp:<EMAIL>"
	description= "apply your comment here"
?>

	<cbc:UBLVersionID>2.0</cbc:UBLVersionID>
	<cbc:CustomizationID>OIOUBL-2.01</cbc:CustomizationID>
	<cbc:ProfileID schemeAgencyID="320" schemeID="urn:oioubl:id:profileid-1.1">Procurement-OrdAdvR-BilSim-1.0</cbc:ProfileID>
	<cbc:ID>A00095678</cbc:ID>
	<cbc:CopyIndicator>false</cbc:CopyIndicator>
	<cbc:UUID>6E09886B-DC6E-439F-82D1-7CCAC7F4E3B3</cbc:UUID>
	<cbc:IssueDate>2006-04-10</cbc:IssueDate>
	<cbc:InvoiceTypeCode listAgencyID="320" listID="urn:oioubl:codelist:invoicetypecode-1.1">380</cbc:InvoiceTypeCode>
	<cbc:DocumentCurrencyCode>DKK</cbc:DocumentCurrencyCode>
	<cbc:AccountingCost>**********</cbc:AccountingCost>
	<cac:OrderReference>
		<cbc:ID>5002701</cbc:ID>
		<cbc:UUID>6E09886B-DC6E-439F-82D1-7CCAC7F4E3B1</cbc:UUID>
		<cbc:IssueDate>2006-04-01</cbc:IssueDate>
	</cac:OrderReference>
	<cac:AccountingSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="DK:CVR">DK16356706</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="DK:CVR">DK16356706</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>PricewaterhouseCoopers</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:AddressFormatCode listAgencyID="320" listID="urn:oioubl:codelist:addressformatcode-1.1">StructuredDK</cbc:AddressFormatCode>
				<cbc:StreetName>Strandvejen</cbc:StreetName>
				<cbc:BuildingNumber>44</cbc:BuildingNumber>
				<cbc:CityName>Hellerup</cbc:CityName>
				<cbc:PostalZone>2900</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>DK</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cbc:CompanyID schemeID="DK:SE">DK16356706</cbc:CompanyID>
				<cac:TaxScheme>
					<cbc:ID schemeAgencyID="320" schemeID="urn:oioubl:id:taxschemeid-1.1">63</cbc:ID>
					<cbc:Name>Moms</cbc:Name>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>PricewaterhouseCoopers</cbc:RegistrationName>
				<cbc:CompanyID schemeID="DK:CVR">DK16356706</cbc:CompanyID>
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:ID>8149</cbc:ID>
				<cbc:Name>Peter Skovborg</cbc:Name>
				<cbc:Telephone>********</cbc:Telephone>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:AccountingSupplierParty>
	<cac:AccountingCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeAgencyID="9" schemeID="GLN">*************</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeAgencyID="9" schemeID="GLN">*************</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>IT- og Telestyrelsen</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:AddressFormatCode listAgencyID="320" listID="urn:oioubl:codelist:addressformatcode-1.1">StructuredDK</cbc:AddressFormatCode>
				<cbc:StreetName>Holsteinsgade</cbc:StreetName>
				<cbc:BuildingNumber>300</cbc:BuildingNumber>
				<cbc:CityName>København Ø</cbc:CityName>
				<cbc:PostalZone>2100</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>DK</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cbc:CompanyID schemeID="DK:SE">DK16356709</cbc:CompanyID>
				<cac:TaxScheme>
					<cbc:ID schemeAgencyID="320" schemeID="urn:oioubl:id:taxschemeid-1.1">63</cbc:ID>
					<cbc:Name>Moms</cbc:Name>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>IT- og Telestyrelsen</cbc:RegistrationName>
				<cbc:CompanyID schemeID="DK:CVR">DK16356709</cbc:CompanyID>
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:ID>90015</cbc:ID>
				<cbc:Name>Lasse Jensen</cbc:Name>
				<cbc:Telephone>2653214</cbc:Telephone>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:AccountingCustomerParty>
	<cac:Delivery>
		<cbc:ActualDeliveryDate>2006-04-15</cbc:ActualDeliveryDate>
	</cac:Delivery>
	<cac:PaymentMeans>
		<cbc:ID>1</cbc:ID>
		<cbc:PaymentMeansCode>42</cbc:PaymentMeansCode>
		<cbc:PaymentDueDate>2006-04-25</cbc:PaymentDueDate>
		<cbc:PaymentChannelCode listAgencyID="320" listID="urn:oioubl:codelist:paymentchannelcode-1.1">DK:BANK</cbc:PaymentChannelCode>
		<cac:PayeeFinancialAccount>
			<cbc:ID>**********</cbc:ID>
			<cbc:PaymentNote>A00095678</cbc:PaymentNote>
			<cac:FinancialInstitutionBranch>
				<cbc:ID>9544</cbc:ID>
			</cac:FinancialInstitutionBranch>
		</cac:PayeeFinancialAccount>
	</cac:PaymentMeans>
	<cac:PaymentTerms>
		<cbc:ID>1</cbc:ID>
		<cbc:PaymentMeansID>1</cbc:PaymentMeansID>
		<cbc:Amount currencyID="DKK">6250.00</cbc:Amount>
	</cac:PaymentTerms>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="DKK">1250.00</cbc:TaxAmount>
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="DKK">5000.00</cbc:TaxableAmount>
			<cbc:TaxAmount currencyID="DKK">1250.00</cbc:TaxAmount>
			<cac:TaxCategory>
				<cbc:ID schemeAgencyID="320" schemeID="urn:oioubl:id:taxcategoryid-1.1">StandardRated</cbc:ID>
				<cbc:Percent>25</cbc:Percent>
				<cac:TaxScheme>
					<cbc:ID schemeAgencyID="320" schemeID="urn:oioubl:id:taxschemeid-1.1">63</cbc:ID>
					<cbc:Name>Moms</cbc:Name>
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="DKK">5000.00</cbc:LineExtensionAmount>
		<cbc:TaxExclusiveAmount currencyID="DKK">1250.00</cbc:TaxExclusiveAmount>
		<cbc:TaxInclusiveAmount currencyID="DKK">6250.00</cbc:TaxInclusiveAmount>
		<cbc:PayableAmount currencyID="DKK">6250.00</cbc:PayableAmount>
	</cac:LegalMonetaryTotal>
	<cac:InvoiceLine>
		<cbc:ID>1</cbc:ID>
		<cbc:InvoicedQuantity unitCode="EA">1.00</cbc:InvoicedQuantity>
		<cbc:LineExtensionAmount currencyID="DKK">5000.00</cbc:LineExtensionAmount>
		<cac:OrderLineReference>
			<cbc:LineID>1</cbc:LineID>
		</cac:OrderLineReference>
		<cac:TaxTotal>
			<cbc:TaxAmount currencyID="DKK">1250.00</cbc:TaxAmount>
			<cac:TaxSubtotal>
				<cbc:TaxableAmount currencyID="DKK">5000.00</cbc:TaxableAmount>
				<cbc:TaxAmount currencyID="DKK">1250.00</cbc:TaxAmount>
				<cac:TaxCategory>
					<cbc:ID schemeAgencyID="320" schemeID="urn:oioubl:id:taxcategoryid-1.1">StandardRated</cbc:ID>
					<cbc:Percent>25</cbc:Percent>
					<cac:TaxScheme>
						<cbc:ID schemeAgencyID="320" schemeID="urn:oioubl:id:taxschemeid-1.1">63</cbc:ID>
						<cbc:Name>Moms</cbc:Name>
					</cac:TaxScheme>
				</cac:TaxCategory>
			</cac:TaxSubtotal>
		</cac:TaxTotal>
		<cac:Item>
			<cbc:Description>Konsulentrapport</cbc:Description>
			<cbc:Name>Konsulentrapport</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID schemeID="n/a">n/a</cbc:ID>
			</cac:SellersItemIdentification>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="DKK">5000.00</cbc:PriceAmount>
			<cbc:BaseQuantity unitCode="EA">1</cbc:BaseQuantity>
			<cbc:OrderableUnitFactorRate>1</cbc:OrderableUnitFactorRate>
		</cac:Price>
	</cac:InvoiceLine>
</Invoice>
