<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_payment_receipt_document">
        <t t-call="web.external_layout">
            <t t-set="o" t-value="o.with_context(lang=lang)"/>
            <t t-set="values" t-value="o._get_payment_receipt_report_values()"/>
            <div class="page">
                <h3><strong id="payment_title">Payment Receipt: <span t-field="o.name">INV0001</span></strong></h3>

                <div class="mb-4 mt-3">
                    <div name="date" class="row">
                        <div class="col-6" t-if="o.date">
                            Payment Date: <span t-field="o.date">2023-01-01</span>
                        </div>
                    </div>
                    <div class="oe_structure"></div>
                    <div class="row">
                        <div class="col-6" t-if="o.partner_type">
                            <t t-if="o.partner_type == 'customer'">
                                Customer:
                            </t>
                            <t t-else="o.partner_type == 'supplier'">
                                Vendor:
                            </t><span t-field="o.partner_id" data-oe-demo="Marc Demo"/>
                        </div>
                        <div name="payment_method"
                             t-if="values['display_payment_method'] and o.payment_method_id"
                             class="col-6">
                            Payment Method: <span t-field="o.payment_method_id.name">Credit card</span>
                        </div>
                    </div>
                    <div class="oe_structure"></div>
                    <div class="row">
                        <div class="col-6" t-if="o.amount">
                            Payment Amount: <span t-field="o.amount" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}">50 USD</span>
                         </div>
                        <div class="col-6" t-if="o.ref">
                            Memo: <span t-field="o.ref">Sample Memo</span>
                         </div>
                    </div>
                </div>

                <div class="oe_structure"></div>
                <table name="invoices"
                       t-if="values['display_invoices']"
                       class="table table-sm">
                    <t t-set="invoices" t-value="o.reconciled_invoice_ids or o.reconciled_bill_ids"/>
                    <!-- Check if invoices include different currencies -->
                    <t t-foreach="invoices" t-as="inv">
                        <t t-if="any(inv.currency_id != par[2].currency_id for par in inv._get_reconciled_invoices_partials()[0])" t-set="otherCurrency" t-value="True"/>
                    </t>
                    <thead>
                        <tr>
                            <th><span>Invoice Date</span></th>
                            <th><span>Invoice Number</span></th>
                            <th><span>Reference</span></th>
                            <!-- Add a column if there are different currencies -->
                            <th t-if="otherCurrency" class="text-end"><span>Amount In Currency</span></th>
                            <th class="text-end"><span>Amount</span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="invoices" t-as="inv">
                            <!-- MOVE -->
                            <t t-if="inv.move_type != 'entry'">
                                <tr>
                                    <td><span t-field="inv.invoice_date">2023-01-01</span></td>
                                    <td><span t-field="inv.name">INV001</span></td>
                                    <td><span t-field="inv.ref" data-oe-demo="Sample Ref"/></td>
                                    <td t-if="otherCurrency"/>
                                    <td class="text-end"><span t-field="inv.amount_total">100.00 USD</span></td>
                                </tr>
                                <!-- PAYMENTS/REVERSALS -->
                                <tr t-foreach="inv._get_reconciled_invoices_partials()[0]" t-as="par">
                                    <t t-set="payment" t-value="par[2].move_id"/>
                                    <td><span t-field="payment.date">2023-01-05</span></td>
                                    <td><span t-field="payment.name">PAY001</span></td>
                                    <td><span t-field="payment.ref" data-oe-demo="Payment Ref"/></td>
                                    <t t-set="amountPayment" t-value="-(par[0].debit_amount_currency if par[2].debit > 0 else par[0].credit_amount_currency)"/>
                                    <t t-set="amountInvoice" t-value="-par[1]"/>
                                    <t t-set="currencyPayment" t-value="payment.currency_id"/>
                                    <t t-set="currencyInvoice" t-value="inv.currency_id"/>
                                    <!-- Fill the column "Amount In Currency" only if necessary -->
                                    <td t-if="otherCurrency" class="text-end"><span t-if="currencyPayment != currencyInvoice" t-out="amountPayment" t-options="{'widget': 'monetary', 'display_currency': currencyPayment}">50.00 EUR</span></td>
                                    <td class="text-end"><span t-out="amountInvoice" t-options="{'widget': 'monetary', 'display_currency': currencyInvoice}">25.00 USD</span></td>
                                </tr>
                                <!-- BALANCE -->
                                <tr>
                                    <td/>
                                    <td><strong>Due Amount for <span t-field="inv.name">INV001</span></strong></td>
                                    <td/>
                                    <td t-if="otherCurrency"/>
                                    <td class="text-end"><strong><span t-field="inv.amount_residual">25.0 USD</span></strong></td>
                                </tr>
                            </t>
                        </t>
                    </tbody>
                </table>
            </div>
            <div class="oe_structure"></div>
        </t>
    </template>

    <template id="report_payment_receipt">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-set="lang" t-value="o.partner_id.lang or o.company_id.partner_id.lang"/>
                <t t-call="account.report_payment_receipt_document" t-lang="lang"/>
            </t>
        </t>
    </template>
</odoo>
