# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
#
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-15 00:51+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"                Manuelle Aktionen können erforderlich sein."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Awaiting arrival"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Warten auf Eingang"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/>Preparation"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>Vorbereitung"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-times\"/> Storniert"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/>Cancelled"
msgstr "<i class=\"fa fa-fw fa-times\"/>Storniert"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> Received"
msgstr "<i class=\"fa fa-fw fa-truck\"/> Erhalten"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Verkäufe</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid ""
"<span class=\"small badge text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> "
"Shipped\n"
"                                    </span>"
msgstr ""
"<span class=\"small badge text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> "
"Versandt\n"
"                                     </span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"<span invisible=\"not display_delivery_alert\">\n"
"                    Some deliveries are already done. Returns can be created "
"from the Delivery Orders.\n"
"                </span>"
msgstr ""
"<span invisible=\"not display_delivery_alert\">\n"
"                    Einige Lieferungen sind bereits erledigt. Retouren "
"können aus den Lieferaufträgen erstellt werden.\n"
"                </span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr "<strong>Kundenreferenz:</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Incoterm:</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be "
"automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted "
"expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to "
"this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Je nach Produktkonfiguration kann die gelieferte Menge automatisch durch "
"einen Mechanismus berechnet werden:\n"
"  - Manuell: Die Menge wird manuell auf der Position eingestellt\n"
"  - Kostenrechnung aus Auslagen: Die Menge ist die Mengensumme aus gebuchten "
"Auslagen\n"
"  - Zeiterfassung: Die Menge ist die Summe der Stunden, die für Aufgaben "
"erfasst wurden, die mit dieser Verkaufsposition verbunden sind.\n"
"  - Lagerbuchungen: Die Menge stammt aus bestätigten Kommissionierungen\n"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "All planned operations included"
msgstr "Alle geplanten Vorgänge enthalten"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr "Spezifische Routen anwenden"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Sobald wie möglich"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Availability"
msgstr "Verfügbarkeit"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Available"
msgstr "Verfügbar"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Available in stock"
msgstr "Auf Lager"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr ""
"Wählen Sie, ob Sie bestimmte Route auf Verkaufsauftragspositionen anwenden "
"möchten."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "Fertigstellungstermin des ersten Lieferauftrags."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Customer reference"
msgstr "Kundenreferenz"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Datum:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr "Standardlagerhaus"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Lieferung"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr "Zustellwarnung"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Lieferaufträge"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_status
msgid "Delivery Status"
msgstr "Zustellungsstatus"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Liefertermin, den Sie dem Kunden zusagen können, errechnet sich bei "
"Dienstleistungsprodukten aus der minimalen Vorlaufzeit der "
"Auftragspositionen. Bei Versand wird die Versandbedingung des Auftrags "
"berücksichtigt, um entweder die minimale oder maximale Vorlaufzeit der "
"Auftragspositionen zu verwenden."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr "Mengenwidget anzeigen"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr ""
"Vergessen Sie nicht, den Partner auf den folgenden Lieferaufträgen zu "
"ändern: %s"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Tatsächliches Datum"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "Ausnahme(n) bei der Kommissionierung:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Ausnahme(n) bei Verkaufsaufträgen:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "Ausnahme(n):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Erwartetes Datum"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Expected Delivery"
msgstr "Erwartete Lieferung"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "Erwartet:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr "Prognostiziertes, erwartetes Datum"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Prognostizierter Bestand"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr "Verfügbare Menge heute"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__full
msgid "Fully Delivered"
msgstr "Vollständig geliefert"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr "Hat verspätete Kommissionierung"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Wenn Sie alle Produkte auf einmal liefern, wird die Lieferzeit auf die "
"längste Vorlaufzeit aller Produkte gesetzt. Andernfalls auf diejenige des "
"zuerst lieferbaren Produktes."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "Betroffene Transfers:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Incoterm"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm_location
msgid "Incoterm Location"
msgstr "Incoterm-Standort"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Incoterm details"
msgstr "Incoterm-Details"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Incoterm:"
msgstr "Incoterm:"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Internationale Lieferbedingungen (Incoterms) sind vordefinierte Konditionen "
"und Bedingungen, die zumeist im internationalen Handel eingesetzt werden."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Lager"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_route
msgid "Inventory Routes"
msgstr "Lagerrouten"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr "Ist Auftragsfertigung"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr "JSON-Daten für Popover-Widget"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Buchungszeile"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Last Delivery Orders"
msgstr "Letzte Lieferaufträge"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Vorlaufzeit"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_lot
msgid "Lot/Serial"
msgstr "Los/Serie"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Manuelle Aktionen können erforderlich sein."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""
"Toleranztage bei Zusagen für Lieferversprechen. Produkte werden um diesen "
"Toleranzzeitraum (in Tagen) früher ausgeliefert als vereinbart, um "
"unerwartetem Verzug in der Lieferkette entgegenzuwirken."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised "
"date, to cope with unexpected delays in the supply chain."
msgstr ""
"Toleranztage bei Zusagen für Lieferversprechen. Produkte werden um diesen "
"Toleranzzeitraum (in Tagen) früher beschafft und ausgeliefert als vereinbart "
"um unerwartetem Verzug in der Lieferkette entgegenzuwirken. (Je nach Kunde "
"mit Vorsicht einsetzen!)"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Methode zum Aktualisieren der gelieferten Menge"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "Erwartetes Lieferdatum verschieben um"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "No future availability"
msgstr "Zukünftig nicht verfügbar"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__pending
msgid "Not Delivered"
msgstr "Nicht geliefert"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Not enough future availability"
msgstr "Zukünftig nicht ausreichend verfügbar"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Anzahl der Tage zwischen Auftragsbestätigung und Auslieferung an den Kunden"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "On"
msgstr "Am"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__partial
msgid "Partially Delivered"
msgstr "Teilweise geliefert"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "Kommissionierrichtlinie"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Beschaffungsgruppe"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produktbewegungen (Lagerbuchung)"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr "Heute verfügbare Menge"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Zu liefernde Menge"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/sale_stock_forecasted/forecasted_details.xml:0
#, python-format
msgid "Quotations"
msgstr "Angebote"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "RETURN"
msgstr "RÜCKGABE"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Remaining demand available at"
msgstr "Restlicher Bedarf verfügbar am"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
#, python-format
msgid "Replenish on Order (MTO)"
msgstr "Auffüllung nach Auftrag (Auftragsfertigung)"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Reserved"
msgstr "Reserviert"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Returns"
msgstr "Retouren"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "Route"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr "Auftragsposition"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Verkaufsauftrag"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Verkaufsaufträge"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_count
msgid "Sale order count"
msgstr "Anzahl Verkaufsaufträge"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Verkaufsanalysebericht"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Verkaufsauftrag"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Verkaufsauftragsstornierung"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkaufsauftragszeile"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Verkaufsauftragszeilen"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_ids
msgid "Sales Orders"
msgstr "Verkaufsaufträge"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr "Verkaufssicherheitstage"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "Planen Sie Lieferungen früher ein, um Verzögerungen zu vermeiden"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Geplantes Datum"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Sicherheitsvorlaufzeit"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "Sicherheitsvorlaufzeit für den Verkauf"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Auswählbar in Verkaufsauftragszeilen"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "Alle Produkte auf einmal versenden"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr "Produkte so schnell wie möglich versenden, mit Lieferrückstanden"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Versandbedingungen"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__started
msgid "Started"
msgstr "Gestartet"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Lagerbuchung"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Lagerbuchungen"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Bestandsauffüllbericht"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Bestandsregel"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Bestandsregelbericht"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Bestandsregelbericht"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
#, python-format
msgid "The delivery"
msgstr "Die Lieferung"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%s\"</strong> To <strong>\"%s\"</"
"strong>,\n"
"                        You should probably update the partner on this "
"document."
msgstr ""
"Die Lieferadresse wurde im Verkaufsauftrag <br/> geändert.\n"
"                        Von <strong>„%s“</strong> in <strong>„%s“</strong>,\n"
"                        Sie sollten wahrscheinlich den Partner auf diesem "
"Dokument aktualisieren."

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/product_catalog/sale_order_line/sale_order_line.js:0
#, python-format
msgid ""
"The ordered quantity cannot be decreased below the amount already delivered. "
"Instead, create a return in your inventory."
msgstr ""
"Die bestellte Menge kann nicht unter die bereits gelieferte Menge gesetzt "
"werden. Legen Sie stattdessen eine Retoure in Ihrem Bestand an."

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
#, python-format
msgid ""
"The ordered quantity of a sale order line cannot be decreased below the "
"amount already delivered. Instead, create a return in your inventory."
msgstr ""
"Die bestellte Menge einer Verkaufsauftragszeile kann nicht unter die bereits "
"gelieferte Menge gesetzt werden. Legen Sie stattdessen eine Retoure in Ihrem "
"Bestand an."

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "This product is replenished on demand."
msgstr "Dieses Produkt wird bei Bedarf aufgefüllt."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Transfers"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "User"
msgstr "Benutzer"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "View Forecast"
msgstr "Prognose anzeigen"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr "Virtuell verfügbar am Datum"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Lagerhaus"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Warning!"
msgstr "Warnung!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Wenn alle Produkte bereit sind"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "Wann soll mit dem Versand begonnen wird"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "abgebrochen"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "Tage"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "von"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "bestellt statt"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "verarbeitet statt"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
#, python-format
msgid "will be late."
msgstr "ist verspätet."
