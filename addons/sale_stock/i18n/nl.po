# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
#
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-15 00:51+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"Handmatige acties zijn mogelijk nodig."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Awaiting arrival"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> In afwachting van aankomst"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/>Preparation"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>Voorbereiding"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-times\"/> Geannuleerd"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/>Cancelled"
msgstr "<i class=\"fa fa-fw fa-times\"/>Geannuleerd"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> Received"
msgstr "<i class=\"fa fa-fw fa-truck\"/> Ontvangen"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Verkoop</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid ""
"<span class=\"small badge text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> "
"Shipped\n"
"                                    </span>"
msgstr ""
"<span class=\"small badge text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> "
"Verzonden\n"
"                                    </span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"<span invisible=\"not display_delivery_alert\">\n"
"                    Some deliveries are already done. Returns can be created "
"from the Delivery Orders.\n"
"                </span>"
msgstr ""
"<span invisible=\"not display_delivery_alert\">\n"
"                    Sommige leveringen zijn al gedaan. Retouren kunnen "
"worden aangemaakt vanuit de leveringsbonnen.\n"
"                </span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr "<strong>Klantreferentie:</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Incoterm:</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be "
"automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted "
"expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to "
"this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Afhankelijk van de productinstellingen, kan de geleverde hoeveelheid "
"automatisch worden berekend met behulp van het mechanisme:\n"
"  - Handmatig: het aantal wordt handmatig op de regel ingesteld\n"
"  - Analytisch van declaraties: het aantal is de som van de geboekte "
"declaraties\n"
"  - Urenstaat: het aantal is de som van de uren die zijn vastgelegd voor "
"taken die aan deze verkoopregel zijn gekoppeld\n"
"  - Voorraadverplaatsingen: het aantal komt van de bevestigd pickings\n"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "All planned operations included"
msgstr "Inclusief alle geplande bewerkingen"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr "Specifieke routes toepassen"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Zo snel mogelijk"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Availability"
msgstr "Beschikbaarheid"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Available"
msgstr "Beschikbaar"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Available in stock"
msgstr "Beschikbaar in voorraad"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr "Kies om een verkooporderregel specifieke route toe te kennen."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "Voltooiingsdatum van de eerste levering. "

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Customer reference"
msgstr "Klantreferentie"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Datum:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr "Standaard magazijn"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Levering"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr "Waarschuwing levering"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Leveringen"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_status
msgid "Delivery Status"
msgstr "Afleverstatus"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Leveringsdatum die je aan de klant kunt beloven, berekend vanaf de minimale "
"levertijd van de orderregels in het geval van diensten-producten. In geval "
"van verzending wordt rekening gehouden met het verzendbeleid van de order om "
"de minimale of maximale levertijd van de orderregels te gebruiken."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr "Toon aantal widget"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr "Vergeet niet de klant te wijzigen op de volgende leveringen: %s"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Boekdatum"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "Uitzondering(en) opgetreden in de picking:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Foutmelding(en) zijn opgetreden op de verkooporder(s):"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "Foutmelding(en):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Datum verwacht"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Expected Delivery"
msgstr "Verwachte levering"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "Verwacht:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr "Prognose datum verwacht"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Voorspelde voorraad"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr "Gratis aantal vandaag"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__full
msgid "Fully Delivered"
msgstr "Volledig geleverd"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr "Heeft te late picking"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Als je alle producten in één keer levert, wordt de levering gepland op basis "
"van de langste levertijd van een product. Anders is deze gebaseerd op de "
"kortste levertijd."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "Geïmpacteerde verplaatsing(en):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Leveringscondities"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm_location
msgid "Incoterm Location"
msgstr "Incoterm locatie"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Incoterm details"
msgstr "Details Incoterm"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Incoterm:"
msgstr "Incoterm:"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"International Commercial Terms (INCOTERMS) zijn een set van "
"voorgedefinieerde commerciële voorwaarden, welke worden gebruikt bij "
"internationaal transport."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Voorraad"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_route
msgid "Inventory Routes"
msgstr "Voorraad routes"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr "is MTO"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr "JSON data voor de popover widget"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Last Delivery Orders"
msgstr "Laatste leveringsbonnen"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Levertijd"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_lot
msgid "Lot/Serial"
msgstr "Partij/Serienummer"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Handmatige acties zijn mogelijk nodig."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""
"Foutmarge op een leverdatum beloofd aan klanten. Producten worden ingepland "
"voor aanvullen en levering, zo veel dagen eerder dan de werkelijk beloofde "
"datum om zo onverwachte vertragingen te kunnen opvangen in de supply chain."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised "
"date, to cope with unexpected delays in the supply chain."
msgstr ""
"Foutmarge op een leverdatum beloofd aan klanten. Producten worden ingepland "
"voor aanvullen en levering, zo veel dagen eerder dan de werkelijk beloofde "
"datum om zo onverwachte vertragingen te kunnen opvangen."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Methode om de geleverde hoeveelheid bij te werken"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "Verschuif geplande leverdatum vooruit met"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "No future availability"
msgstr "Geen voorraad in de toekomst"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__pending
msgid "Not Delivered"
msgstr "Niet geleverd"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Not enough future availability"
msgstr "Niet genoeg voorraad in de toekomst"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Aantal dagen tussen het bevestigen van de order en het werkelijk leveren van "
"de producten aan de klant."

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "On"
msgstr "Op"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__partial
msgid "Partially Delivered"
msgstr "Gedeeltelijk geleverd"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "Leveringsbeleid"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Aanvulgroep"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product"
msgstr "Product"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Productverplaatstingen (voorraadverplaatsingsregels)"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr "Aantal vandaag beschikbaar"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Aantal te leveren"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/sale_stock_forecasted/forecasted_details.xml:0
#, python-format
msgid "Quotations"
msgstr "Offertes"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "RETURN"
msgstr "RETOUR"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Remaining demand available at"
msgstr "Resterende vraag beschikbaar op"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
#, python-format
msgid "Replenish on Order (MTO)"
msgstr "Aanvullen per order (MTO)"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Reserved"
msgstr "Gereserveerd"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Returns"
msgstr "Retouren"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "Route"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr "Orderregel"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Verkooporder"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Verkooporders"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_count
msgid "Sale order count"
msgstr "Totaal verkooporders"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Verkoopanalyserapport"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Verkooporder"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Verkooporder annuleer"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Verkooporderregels"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_ids
msgid "Sales Orders"
msgstr "Verkooporders"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr "Verkopen veiligheidsdagen"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "Plan levering eerder om vertragingen te voorkomen"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Geplande datum"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Beveiligingstijd"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "Veiligheid leveringstijd voor verkoop"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Selecteerbaar op verkooporderregels"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "Verzend alle producten in één keer"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr "Verzend producten wanneer ze beschikbaar zijn en sta backorders toe"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Leveringsbeleid"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__started
msgid "Started"
msgstr "Gestart"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Voorraadverplaatsing"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Voorraadverplaatsingen"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Voorraadaanvulling rapport"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Voorraadregel"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Voorraadroutes rapport"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Voorraadregelrapportage"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
#, python-format
msgid "The delivery"
msgstr "De levering"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%s\"</strong> To <strong>\"%s\"</"
"strong>,\n"
"                        You should probably update the partner on this "
"document."
msgstr ""
"Het afleveradres is gewijzigd op de verkooporder <br/>\n"
" Van \"<strong>\" %s\"</strong> naar <strong>\"%s\"</strong>,\n"
" Je moet waarschijnlijk de relatie in dit document bijwerken."

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/product_catalog/sale_order_line/sale_order_line.js:0
#, python-format
msgid ""
"The ordered quantity cannot be decreased below the amount already delivered. "
"Instead, create a return in your inventory."
msgstr ""
"De bestelde hoeveelheid kan niet lager worden dan de reeds geleverde "
"hoeveelheid. Maak in plaats daarvan een retour aan."

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
#, python-format
msgid ""
"The ordered quantity of a sale order line cannot be decreased below the "
"amount already delivered. Instead, create a return in your inventory."
msgstr ""
"De bestelde hoeveelheid kan niet lager zijn dan de reeds geleverde "
"hoeveelheid. Maak liever een retourzending aan."

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "This product is replenished on demand."
msgstr "Dit voorraad van product wordt op aanvraag aangevuld."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Verplaatsing"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Verplaatsingen"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "User"
msgstr "Gebruiker"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "View Forecast"
msgstr "Bekijk prognose"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr "Virtueel beschikbaar op"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Magazijn"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Warning!"
msgstr "Waarschuwing!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Wanneer alle producten beschikbaar zijn"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "Wanneer verzenden starten"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "geannuleerd"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "dagen"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "of"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "besteld in plaats van"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "verwerkt in plaats van"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
#, python-format
msgid "will be late."
msgstr "zat te laat zijn."
