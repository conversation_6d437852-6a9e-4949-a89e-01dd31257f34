# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
#
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2025
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-15 00:51+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? "
"1 : 2;\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"                Acciones manuales pueden ser requeridas."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Awaiting arrival"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> En espera de llegada"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/>Preparation"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>Preparación"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-times\"/> Cancelado"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/>Cancelled"
msgstr "<i class=\"fa fa-fw fa-times\"/>Cancelado"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> Received"
msgstr "<i class=\"fa fa-fw fa-truck\"/> Recibido"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Ventas</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid ""
"<span class=\"small badge text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> "
"Shipped\n"
"                                    </span>"
msgstr ""
"<span class=\"small badge text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> "
"Enviado\n"
"                                    </span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"<span invisible=\"not display_delivery_alert\">\n"
"                    Some deliveries are already done. Returns can be created "
"from the Delivery Orders.\n"
"                </span>"
msgstr ""
"<span invisible=\"not display_delivery_alert\">\n"
"                   Ya se realizaron algunas entregas. Se pueden crear "
"devoluciones a partir de las órdenes de entrega.\n"
"                </span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr "<strong>Referencia del cliente:</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Incoterm:</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be "
"automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted "
"expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to "
"this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Según la configuración del producto, se puede calcular automáticamente la "
"cantidad enviada:\n"
"  - Manual: la cantidad se establece manualmente en la línea\n"
"  - Analítica de gastos: la cantidad equivale a la suma de los gastos "
"publicados\n"
"  - Partes de horas: la cantidad equivale a la suma de las horas registradas "
"en las tareas vinculadas a esta línea de venta\n"
"  - Movimientos del inventario: la cantidad viene de los albaranes "
"confirmados\n"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "All planned operations included"
msgstr "Todas las operaciones planificadas incluidas"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr "Aplicar rutas específicas"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Lo antes posible"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Availability"
msgstr "Disponibilidad"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Available"
msgstr "Disponible"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Available in stock"
msgstr "Unidades disponibles"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr "Elija aplicar rutas específicas para líneas del pedido de venta."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "Fecha de finalización de la primera orden de entrega."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Customer reference"
msgstr "Referencia del cliente"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Fecha:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr "Almacén por defecto"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Entrega"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr "Alerta de entrega"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Órdenes de entrega"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_status
msgid "Delivery Status"
msgstr "Estado de entrega"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Fecha de entrega que puede prometer al cliente, en el caso de los productos "
"de servicio, se calcula a partir del plazo mínimo de las líneas del pedido. "
"En el caso de los envíos, se tendrá en cuenta la política de envíos del "
"pedido para utilizar el plazo mínimo o máximo de las líneas del mismo."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr "Mostrar widget de cantidad"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr "No olvide cambiar el contacto en las siguientes órdenes de entrega: %s"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Fecha efectiva"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "Excepcion(es) producida(s) en el albarán:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Excepción(es) ocurrida(s) en pedidos de venta:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "Excepción(es):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Fecha prevista"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Expected Delivery"
msgstr "Entrega prevista"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "Previsto:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr "Fecha prevista del pronóstico"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Stock pronosticado"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr "Ctd. disponible hoy"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__full
msgid "Fully Delivered"
msgstr "Completamente entregado"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr "Tiene un picking retrasado"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Si entrega todos los productos a la vez, la orden de entrega se programará "
"en función del mayor plazo de entrega del producto. De lo contrario, se "
"basará en el más corto."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "Traslado(s) impactado(s):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Incoterm"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm_location
msgid "Incoterm Location"
msgstr "Ubicación del Incoterm"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Incoterm details"
msgstr "Detalles del incoterm"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Incoterm:"
msgstr "Incoterm:"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Los términos de comercio internacional (Incoterms) son una serie de "
"condiciones comerciales usadas en las transacciones internacionales."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Inventario"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_route
msgid "Inventory Routes"
msgstr "Rutas de inventario"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr "Es una fabricación sobre pedido"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr "Datos JSON para el widget de ventana emergente"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Last Delivery Orders"
msgstr "Últimas órdenes de entrega"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Plazo de entrega"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_lot
msgid "Lot/Serial"
msgstr "Lote/serie "

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Acciones manuales pueden ser necesarias."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""
"Margen de error para fechas prometidas a los clientes. La entrega de los "
"productos quedará programada días antes de la fecha prometida para hacer "
"frente a posibles retrasos inesperados en la cadena de suministro."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised "
"date, to cope with unexpected delays in the supply chain."
msgstr ""
"Margen de error para las fechas prometidas a los clientes. Los productos "
"serán planificados para el abastecimiento y entrega días antes de la fecha "
"prometida actual, para lidiar con posibles retrasos inesperados en la cadena "
"de suministro."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Método para actualizar la cantidad entregada"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "Adelantar las fechas de entrega previstas"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "No future availability"
msgstr "Sin disponibilidad en el futuro"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__pending
msgid "Not Delivered"
msgstr "No entregado"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Not enough future availability"
msgstr "No hay suficiente disponibilidad futura"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Número de días entre la confirmación del pedido y la entrega de los "
"productos al cliente"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "On"
msgstr "Al"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__partial
msgid "Partially Delivered"
msgstr "Parcialmente entregado"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "Política de picking"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Grupo de abastecimiento"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product"
msgstr "Producto"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimientos de producto (línea de movimiento de stock)"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr "Ctd. disponible hoy"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Ctd. a entregar"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/sale_stock_forecasted/forecasted_details.xml:0
#, python-format
msgid "Quotations"
msgstr "Presupuestos"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "RETURN"
msgstr "DEVOLUCIÓN"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Remaining demand available at"
msgstr "La demanda restante está disponible en"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
#, python-format
msgid "Replenish on Order (MTO)"
msgstr "Reposición bajo pedido (fabricación sobre pedido)"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Reserved"
msgstr "Reservado"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Returns"
msgstr "Devoluciones"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "Ruta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr "Línea de venta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Pedido de venta"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Pedidos de venta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_count
msgid "Sale order count"
msgstr "Nº de pedidos de venta"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Informe de análisis de ventas"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Pedido de venta"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Cancelar pedido de venta"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de pedido de venta"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Líneas de pedido de venta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_ids
msgid "Sales Orders"
msgstr "Pedidos de venta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr "Días de seguridad para ventas"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "Programe las entregas antes para evitar retrasos"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Plazo de seguridad"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "Plazo de seguridad para ventas"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Seleccionable en las líneas de pedidos de venta"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "Enviar todos los productos a la vez"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr ""
"Enviar los productos tan pronto como estén disponibles, con los pedidos en "
"espera"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Política de envío"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__started
msgid "Started"
msgstr "Inició"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de stock"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Movimientos de stock"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Informe de reposición de stock"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Regla de inventario"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Informe de reglas de inventario"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Informe de regla de inventario"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
#, python-format
msgid "The delivery"
msgstr "La entrega"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%s\"</strong> To <strong>\"%s\"</"
"strong>,\n"
"                        You should probably update the partner on this "
"document."
msgstr ""
"La dirección de entrega ha sido cambiada en el pedido de venta<br/>\n"
"                        De <strong>\"%s\"</strong> a <strong>\"%s\"</"
"strong>,\n"
"                        Probablemente debe actualizar el contacto en este "
"documento."

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/product_catalog/sale_order_line/sale_order_line.js:0
#, python-format
msgid ""
"The ordered quantity cannot be decreased below the amount already delivered. "
"Instead, create a return in your inventory."
msgstr ""
"La cantidad pedida no puede disminuir por debajo del importe que ya se "
"entregó. En su lugar, cree una devolución en su inventario."

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
#, python-format
msgid ""
"The ordered quantity of a sale order line cannot be decreased below the "
"amount already delivered. Instead, create a return in your inventory."
msgstr ""
"La cantidad pedida de una línea de pedido de venta no puede disminuirse por "
"debajo de la cantidad ya entregada. En su lugar, cree una devolución en su "
"inventario."

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "This product is replenished on demand."
msgstr "Este producto se repone bajo demanda."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Traslado"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Traslados"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "User"
msgstr "Usuario"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "View Forecast"
msgstr "Ver pronóstico"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr "Disponibilidad virtual a la fecha"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Almacén"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Warning!"
msgstr "¡Advertencia!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Cuando todos los productos estén listos"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "Cuándo comenzar el envío"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "cancelado"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "días"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "de"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "pedido en lugar de"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "procesado en lugar de"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
#, python-format
msgid "will be late."
msgstr "se retrasará."
