# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * spreadsheet_dashboard_purchase
#
# Translators:
# Wil Odoo, 2024
#
# "<PERSON> (ngto)" <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2025-08-15 14:21+0000\n"
"Last-Translator: \"<PERSON> (ngto)\" <<EMAIL>>\n"
"Language-Team: Chinese (Traditional Han script) <https://translate.odoo.com/"
"projects/odoo-17/spreadsheet_dashboard_purchase/zh_Hant/>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid " days"
msgstr " 天"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Amount"
msgstr "金額"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Buyer"
msgstr "買方"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Country"
msgstr "國家"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Current"
msgstr "目前"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Days to receive"
msgstr "接收天數"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Lines"
msgstr "明細列表"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Order"
msgstr "訂單"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Ordered"
msgstr "已訂購"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Orders"
msgstr "訂單"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Period"
msgstr "期間"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "上一頁"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Partner Country"
msgstr "按合作夥伴國家/地區劃分的採購分析"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Purchase Representative"
msgstr "採購代表的採購分析"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Vendor"
msgstr "按供應商劃分的採購分析"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchase Orders by Untaxed Amount"
msgstr "按未稅金額列出的採購訂單"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchased"
msgstr "已採購"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Qty ordered"
msgstr "訂購數量"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Quantity ordered"
msgstr "訂購數量"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Source"
msgstr "來源"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Sourcing by Country"
msgstr "按國家/地區採購"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Top Buyers"
msgstr "採購人員排行"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Top Orders"
msgstr "熱門訂單"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Top Vendors"
msgstr "供應商排行"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Top Vendors by Amount"
msgstr "按金額排名的頂級供應商"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Untaxed total"
msgstr "未稅總額"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Vendor"
msgstr "供應商"

#. module: spreadsheet_dashboard_purchase
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_purchase.spreadsheet_dashboard_vendors
msgid "Vendors"
msgstr "供應商"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "last period"
msgstr "上個時段"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "since last period"
msgstr "較上一期間"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "stats - current"
msgstr "統計數據 - 當前"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "stats - previous"
msgstr "統計數據 - 以前"
