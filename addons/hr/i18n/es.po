# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr
#
# Translators:
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# "<PERSON><PERSON> (lman)" <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2025-08-06 09:55+0000\n"
"Last-Translator: \"<PERSON><PERSON> (lman)\" <<EMAIL>>\n"
"Language-Team: Spanish <https://translate.odoo.com/projects/odoo-17/hr/es/>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n != 0 && n % 1000000 == 0)"
" ? 1 : 2);\n"
"X-Generator: Weblate 5.12.2\n"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email_amount
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email_amount
msgid "# emails to send"
msgstr "N.° de correos electrónicos a enviar"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_job.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: hr
#: model:ir.actions.report,print_report_name:hr.hr_employee_print_badge
msgid "'Badge - %s' % (object.name).replace('/', '')"
msgstr "'Credencial - %s' % (object.name).replace('/', '')"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Onsite Interview"
msgstr "1 Entrevista in situ"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Phone Call"
msgstr "1 llamada telefónica"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 días al año, con<br> 6 a su elección."

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "2 open days"
msgstr "2 días disponibles"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "4 Days after Interview"
msgstr "4 días después de la entrevista"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"<b>Congratulations!</b> May I recommend you to setup an <a "
"href=\"%s\">onboarding plan?</a>"
msgstr ""
"<b>¡Enhorabuena!</b> ¿Puedo recomendarle que configure un <a "
"href=\"%s\">plan de incorporación?</a>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "<i class=\"fa fa-fw me-2 fa-envelope text-primary\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-fw me-2 fa-envelope text-primary\" title=\"Email\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "<i class=\"fa fa-fw me-2 fa-phone text-primary\" title=\"Phone\"/>"
msgstr "<i class=\"fa fa-fw me-2 fa-phone text-primary\" title=\"Phone\"/>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<small><b>READ</b></small>"
msgstr "<small><b>LEER</b></small>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"<span class=\"flex-shrink-0 ml8 me-2\">IP Addresses (comma-separated)</span>"
msgstr ""
"<span class=\"flex-shrink-0 ml8 me-2\">Direcciones IP (separadas por "
"comas)</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"<span class=\"flex-shrink-0 ml8 me-2\">Minimum number of emails to "
"send</span>"
msgstr ""
"<span class=\"flex-shrink-0 ml8 me-2\">Número mínimo de correos electrónicos"
" a enviar</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span class=\"fw-bolder\">Reporting</span>"
msgstr "<span class=\"fw-bolder\">Informes</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Close "
"Activities</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Cerrar "
"actividades</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Detailed "
"Reason</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Motivo "
"detallado</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label cursor-default\">HR Info</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Información de "
"RR. HH.</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Not Connected\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    No conectado\n"
"                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "<span class=\"o_stat_text\">Connected Since</span>"
msgstr "<span class=\"o_stat_text\">Conectado desde</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_form
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr "<span class=\"o_stat_text\">Empleado</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr "<span class=\"text-muted small\">Días para recibir una oferta</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Procesar</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Tiempo para responder</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<span>Km</span>"
msgstr "<span>Km</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "<span>new Employees</span>"
msgstr "<span>nuevos empleados</span>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "A full-time position <br>Attractive salary package."
msgstr "Puesto de tiempo completo<br> Sueldo atractivo."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_user_uniq
msgid "A user cannot be linked to multiple employees in the same company."
msgstr ""
"Un usuario no puede estar vinculado a varios empleados en la misma compañía."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__absent
msgid "Absent"
msgstr "Ausente"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Achieve monthly sales objectives"
msgstr "Alcanzar los objetivos de ventas mensuales"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__active
#: model:ir.model.fields,field_description:hr.field_hr_employee__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__active
#: model:ir.model.fields,field_description:hr.field_hr_job__active
#: model:ir.model.fields,field_description:hr.field_hr_work_location__active
msgid "Active"
msgstr "Activo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_plan
msgid "Activity Plan"
msgstr "Plan de actividad"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_plan
msgid "Activity Planning"
msgstr "Planificación de actividades"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Activity by"
msgstr "Actividad por"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Plantilla del plan de actividad"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Asistente para planificar actividades"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Add a new employee"
msgstr "Añadir un nuevo empleado"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.mail_activity_plan_action
msgid "Add a new plan"
msgstr "Añadir un nuevo plan"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_description
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_description
msgid "Additional Information"
msgstr "Información adicional"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"Additional Information: \n"
" %(description)s"
msgstr ""
"Información adicional: \n"
" %(description)s"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__additional_note
#: model:ir.model.fields,field_description:hr.field_res_users__additional_note
msgid "Additional Note"
msgstr "Nota adicional"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Additional languages"
msgstr "Idiomas adicionales"

#. module: hr
#: model:hr.department,name:hr.dep_administration
msgid "Administration"
msgstr "Administración"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Administrative Work"
msgstr "Trabajo administrativo"

#. module: hr
#: model:res.groups,name:hr.group_hr_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_presence
msgid "Advanced Presence Control"
msgstr "Control de presencia avanzado"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Advanced presence of employees"
msgstr "Presencia avanzada de empleados"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:hr.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "Seguridad de contacto del alias"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data"
msgstr "Permita a los empleados actualizar sus propios datos"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data."
msgstr "Permita a los empleados actualizar sus propios datos."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Application Settings"
msgstr "Ajustes de solicitud"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Apply"
msgstr "Aplicar"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Approvers"
msgstr "Aprobadores"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Archived"
msgstr "Archivado"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"As an employee of our company, you will <b>collaborate with each department to create and deploy\n"
"                                disruptive products.</b> Come work at a growing company that offers great benefits with opportunities to\n"
"                                moving forward and learn alongside accomplished leaders. We're seeking an experienced and outstanding member of staff.\n"
"                                <br><br>\n"
"                                This position is both <b>creative and rigorous</b> by nature you need to think outside the box.\n"
"                                We expect the candidate to be proactive and have a \"get it done\" spirit. To be successful,\n"
"                                you will have solid solving problem skills."
msgstr ""
"Cada uno de nuestros empleados<b>colabora con todos los departamentos para crear y desplegar \n"
"                                productos innovadores.</b> Nuestra compañía ofrece grandes beneficios y oportunidades de\n"
"                                crecimiento y desarrollo junto con líderes exitosos. Buscamos una persona con experiencia y que sea excepcional.\n"
"                                <br><br>\n"
"                                Este puesto requiere <b>creatividad y atención al detalle</b> innata, debe estar acostumbrado a pensar fuera de lo convencional.\n"
"                                Esperamos que el candidato sea proactivo y tenga una mentalidad orientada a resultados. Tendrá mucho éxito si\n"
"                                también cuenta con habilidades de resolución de problemas."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Asignación"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_job__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Attendance"
msgstr "Asistencia"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Attendance/Point of Sale"
msgstr "Asistencia/Punto de Venta"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__mail_alias__alias_contact__employees
msgid "Authenticated Employees"
msgstr "Empleados autenticados"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.discuss_channel_view_form
msgid "Auto Subscribe Departments"
msgstr "Suscripción automática a departamentos"

#. module: hr
#: model:ir.model.fields,help:hr.field_discuss_channel__subscription_department_ids
msgid "Automatically subscribe members of those departments to the channel."
msgstr "Suscribe automáticamente los miembros de esos departamentos al canal."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Autonomy"
msgstr "Autonomía"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Available"
msgstr "Disponible"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.mail_activity_plan_view_form
msgid "Available for all Departments"
msgstr "Disponible para todos los departamentos"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1920
msgid "Avatar"
msgstr "Avatar"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Away"
msgstr "Ausente"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__bachelor
msgid "Bachelor"
msgstr "Título de grado"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Bachelor Degree or Higher"
msgstr "Título de grado o superior"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__barcode
#: model:ir.model.fields,field_description:hr.field_res_users__barcode
msgid "Badge ID"
msgstr "ID de credencial"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__bank_account_id
msgid "Bank Account Number"
msgstr "Número de cuenta bancaria"

#. module: hr
#: model:ir.model,name:hr.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Cuentas bancarias"

#. module: hr
#: model:ir.model,name:hr.model_base
msgid "Base"
msgstr "Base"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip
msgid "Based on IP Address"
msgstr "Basado en la dirección IP"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_attendance
msgid "Based on attendances"
msgstr "Basado en asistencias"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email
msgid "Based on number of emails sent"
msgstr "Basado en el número de correos electrónicos enviados"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_login
msgid "Based on user status in system"
msgstr "Basado en el estado del usuario en el sistema"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleado básico"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/background_image/background_image.xml:0
#, python-format
msgid "Binary file"
msgstr "Archivo binario"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__can_edit
msgid "Can Edit"
msgstr "Puede editar"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Cancel"
msgstr "Cancelar"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__certificate
#: model:ir.model.fields,field_description:hr.field_res_users__certificate
msgid "Certificate Level"
msgstr "Nivel de certificado"

#. module: hr
#: model:ir.actions.act_window,name:hr.res_users_action_my
msgid "Change my Preferences"
msgstr "Cambiar mis preferencias"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/employee_chat/employee_chat.xml:0
#, python-format
msgid "Chat"
msgstr "Chat"

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr "Director ejecutivo"

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr "Director técnico"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__child_ids
msgid "Child Departments"
msgstr "Departamentos hijos"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship"
msgstr "Nacionalidad"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "City"
msgstr "Ciudad"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,field_description:hr.field_res_users__coach_id
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__coach
msgid "Coach"
msgstr "Mentor"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
#, python-format
msgid "Coach of employee %s is not set."
msgstr "El mentor del empleado %s no está establecido."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__code
msgid "Code"
msgstr "Código"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Color"
msgstr "Color"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__color
#: model:ir.model.fields,field_description:hr.field_hr_employee__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__color
msgid "Color Index"
msgstr "Índice de Colores"

#. module: hr
#: model:ir.model,name:hr.model_res_company
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "Compañías"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__company_id
#: model:ir.model.fields,field_description:hr.field_hr_job__company_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__company_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "Compañía"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_id
msgid "Company Country"
msgstr "País de la compañía"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Company Logo"
msgstr "Logotipo de la compañía"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__resource_calendar_id
msgid "Company Working Hours"
msgstr "Horas laborables de la compañía"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_id
msgid "Company employee"
msgstr "Empleado de la compañía"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__complete_name
msgid "Complete Name"
msgstr "Nombre completo"

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Configuration"
msgstr "Configuración"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr "Consultor"

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Contact Information"
msgstr "Información de contacto"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_contact
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_contact
msgid "Contact Name"
msgstr "Nombre del contacto"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_phone
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_phone
msgid "Contact Phone"
msgstr "Teléfono de contacto"

#. module: hr
#: model:ir.model,name:hr.model_hr_contract_type
msgid "Contract Type"
msgstr "Tipo de contrato"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_contract_type_view_tree
msgid "Contract Types"
msgstr "Tipos de contrato"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__contractor
msgid "Contractor"
msgstr "Contratista"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__country_id
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Country"
msgstr "País"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_code
msgid "Country Code"
msgstr "Código de país"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__country_of_birth
msgid "Country of Birth"
msgstr "País de nacimiento"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_type
msgid "Cover Image"
msgstr "Imagen de portada"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_date
msgid "Create Date"
msgstr "Fecha de creación"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
msgid "Create Employee"
msgstr "Crear empleado"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.server,name:hr.action_hr_employee_create_user
#, python-format
msgid "Create User"
msgstr "Crear usuario"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid "Create a new department"
msgstr "Crear un nuevo departamento"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_contract_type_action
msgid "Create a new employment type"
msgstr "Crear un nuevo tipo de empleado"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_work_location_action
msgid "Create a new work location"
msgstr "Crear una nueva ubicación de trabajo"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Create content that will help our users on a daily basis"
msgstr "Crear contenido que ayude a nuestros usuarios diariamente"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Create employee"
msgstr "Crear empleado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_date
#: model:ir.model.fields,field_description:hr.field_hr_department__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_date
#: model:ir.model.fields,field_description:hr.field_hr_job__create_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_employee
msgid "Current Number of Employees"
msgstr "Número actual de empleados"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Customer Relationship"
msgstr "Relación con el cliente"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__birthday
#: model:ir.model.fields,field_description:hr.field_res_users__birthday
msgid "Date of Birth"
msgstr "Fecha de nacimiento"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_departure_reason.py:0
#, python-format
msgid "Default departure reasons cannot be deleted."
msgstr "Los motivos de salida por defecto no se pueden eliminar."

#. module: hr
#: model_terms:hr.job,description:hr.job_ceo
msgid ""
"Demonstration of different Odoo services for each client and convincing the client about functionality of the application.\n"
"The candidate should have excellent communication skills.\n"
"Relationship building and influencing skills\n"
"Expertise in New Client Acquisition (NCAs) and Relationship Management.\n"
"Gathering market and customer information.\n"
"Coordinating with the sales and support team for adopting different strategies\n"
"Reviewing progress and identifying opportunities and new areas for development.\n"
"Building strong relationships with clients / customers for business growth profitability.\n"
"Keep regular interaction with key clients for better extraction and expansion."
msgstr ""
"Demostración de diferentes servicios de Odoo para cada cliente y convencer al cliente sobre la funcionalidad de la aplicación.\n"
"El candidato deberá tener excelentes habilidades de comunicación.\n"
"Habilidades de creación de relaciones y de influencia.\n"
"Experiencia en adquisición de nuevos clientes (NCA) y gestión de relaciones.\n"
"Recopilación de información sobre el mercado y los clientes.\n"
"Coordinación con el equipo de ventas y de apoyo para adoptar diferentes estrategias.\n"
"Revisión de los progresos e identificación de oportunidades y nuevas áreas de desarrollo.\n"
"Construir relaciones sólidas con los clientes / clientes para la rentabilidad del crecimiento del negocio.\n"
"Mantener una interacción regular con los clientes clave para una mejor extracción y expansión."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card/avatar_card_popover_patch.xml:0
#: model:ir.model,name:hr.model_hr_department
#: model:ir.model.fields,field_description:hr.field_hr_employee__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__department_id
#: model:ir.model.fields,field_description:hr.field_hr_job__department_id
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan__department_id
#: model:ir.model.fields,field_description:hr.field_mail_activity_schedule__department_id
#: model:ir.model.fields,field_description:hr.field_res_users__department_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
#, python-format
msgid "Department"
msgstr "Departamento"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__name
msgid "Department Name"
msgstr "Nombre del departamento"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
#, python-format
msgid "Department Organization"
msgstr "Organización del departamento"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_department_kanban_action
#: model:ir.actions.act_window,name:hr.hr_department_tree_action
#: model:ir.ui.menu,name:hr.menu_hr_department_kanban
#: model:ir.ui.menu,name:hr.menu_hr_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr "Departamentos"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Departure"
msgstr "Salida"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_date
msgid "Departure Date"
msgstr "Fecha de salida"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_reason
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_reason_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_reason_id
msgid "Departure Reason"
msgstr "Motivo de salida"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_departure_reason_action
#: model:ir.ui.menu,name:hr.menu_hr_departure_reason_tree
msgid "Departure Reasons"
msgstr "Motivos de salida"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Asistente de salida"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Dependant"
msgstr "Dependiente"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__child_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__child_ids
msgid "Direct subordinates"
msgstr "Subordinados directos"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee
msgid "Directory"
msgstr "Directorio"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Discard"
msgstr "Descartar"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Discover our products."
msgstr "Descubra nuestros productos."

#. module: hr
#: model:ir.model,name:hr.model_discuss_channel
msgid "Discussion Channel"
msgstr "Canal de conversaciones"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__display_name
#: model:ir.model.fields,field_description:hr.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr.field_hr_work_location__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Display remote work settings for each employee and dedicated reports. "
"Presence icons will be updated with remote work location."
msgstr ""
"Muestre los ajustes de teletrabajo para cada empleado e informes "
"específicos. Los iconos de asistencia se actualizarán con la ubicación de "
"teletrabajo."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__divorced
msgid "Divorced"
msgstr "Divorciado"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__doctor
msgid "Doctor"
msgstr "Doctor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__driving_license
msgid "Driving License"
msgstr "Permiso de conducir"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                        You can make a real contribution to the success of the company.\n"
"                        <br>\n"
"                        Several activities are often organized all over the year, such as weekly\n"
"                        sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Cada empleado tiene la oportunidad de ver el impacto de su trabajo.\n"
"                        Usted puede hacer una contribución real al éxito de la compañía.\n"
"                        <br>\n"
"                        Se organizan varias actividades a lo largo del año, como actividades deportivas\n"
"                        semanales, eventos para fomentar el trabajo en equipo, una convivencia al mes, ¡y mucho más!"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Eat &amp; Drink"
msgstr "Comer y beber"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Education"
msgstr "Educación"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Email"
msgstr "Correo electrónico"

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr "Alias de correo electrónico"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Emergency"
msgstr "Emergencia"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
#: model:ir.model,name:hr.model_hr_employee
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__employee_id
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__employee
#: model:ir.ui.menu,name:hr.menu_config_employee
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
#, python-format
msgid "Employee"
msgstr "Empleado"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr "Categoría de empleado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_count
msgid "Employee Count"
msgstr "Número de empleados"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_employee_self_edit
msgid "Employee Editing"
msgstr "Edición de empleados"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Employee Image"
msgstr "Imagen del empleado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_lang
msgid "Employee Lang"
msgstr "Idioma del empleado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__name
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Employee Name"
msgstr "Nombre del empleado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__employee_properties_definition
msgid "Employee Properties"
msgstr "Propiedades del empleado"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.model.fields,field_description:hr.field_res_users__category_ids
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr "Etiquetas del empleado"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/archive_employee_hook.js:0
#, python-format
msgid "Employee Termination"
msgstr "Terminación del empleado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_type
#: model:ir.model.fields,field_description:hr.field_res_users__employee_type
msgid "Employee Type"
msgstr "Tipo de empleado"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Employee Update Rights"
msgstr "Derechos de actualización del empleado"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__bank_account_id
#: model:ir.model.fields,help:hr.field_res_users__employee_bank_account_id
msgid "Employee bank account to pay salaries"
msgstr "Cuenta bancaria del empleado en la que pagar el salario"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_bank_account_id
msgid "Employee's Bank Account Number"
msgstr "Número de cuenta bancaria del empleado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_country_id
msgid "Employee's Country"
msgstr "País del empleado"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr "Nombre del empleado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_resource_calendar_id
msgid "Employee's Working Hours"
msgstr "Horas laborables del empleado"

#. module: hr
#: model:ir.actions.act_window,name:hr.act_employee_from_department
#: model:ir.actions.act_window,name:hr.hr_employee_public_action
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.model.fields,field_description:hr.field_res_partner__employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_employee_payroll
#: model:ir.ui.menu,name:hr.menu_hr_employee_user
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_activity
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Employees"
msgstr "Empleados"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_partner__employees_count
#: model:ir.model.fields,field_description:hr.field_res_users__employees_count
msgid "Employees Count"
msgstr "Número de empleados"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_employee_tree
msgid "Employees Structure"
msgstr "Estructura de empleados"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr "Etiquetas de empleados"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__contract_type_id
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Employment Type"
msgstr "Tipo de contrato"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_contract_type_action
#: model:ir.ui.menu,name:hr.menu_view_hr_contract_type
msgid "Employment Types"
msgstr "Tipos de empleados"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Enrich employee profiles with skills and resumes"
msgstr "Enriquece los perfiles de los empleados con habilidades y currículums"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Expand your knowledge of various business industries"
msgstr "Amplíe su conocimiento de diversas industrias comerciales"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__expected_employees
msgid ""
"Expected number of employees for this job position after new recruitment."
msgstr ""
"Número previsto de empleados para este puesto tras las nuevas "
"contrataciones."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Experience in writing online content"
msgstr "Experiencia en redacción de contenido en línea"

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr "Desarrollador experimentado"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Family Status"
msgstr "Situación familiar"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__female
msgid "Female"
msgstr "Femenino"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_field
#: model:ir.model.fields,field_description:hr.field_res_users__study_field
msgid "Field of Study"
msgstr "Campo de estudio"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_fired
msgid "Fired"
msgstr "Despedido"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome p. ej. fa-tasks"

#. module: hr
#. odoo-python
#: code:addons/hr/models/discuss_channel.py:0
#, python-format
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the department "
"auto-subscription."
msgstr ""
"Para %(channels)s, channel_type debe ser \"canal\" para tener la suscripción"
" automática por departamento."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__freelance
msgid "Freelancer"
msgstr "Autónomo"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Fruit, coffee and <br>snacks provided."
msgstr "Ofrecemos fruta, café <br>y aperitivos."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_full_time
msgid "Full-Time"
msgstr "Tiempo completo"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__gender
#: model:ir.model.fields,field_description:hr.field_res_users__gender
msgid "Gender"
msgstr "Género"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Generate"
msgstr "Generar"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Give more details about the reason of archiving the employee."
msgstr "Proporcione más detalles sobre el motivo para archivar el empleado."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Google Adwords experience"
msgstr "Experiencia de Google Adwords"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__graduate
msgid "Graduate"
msgstr "Licenciado"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Great team of smart people, in a friendly and open culture"
msgstr "Gran equipo de gente inteligente, en una cultura amigable y abierta"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_discuss_channel__subscription_department_ids
msgid "HR Departments"
msgstr "Departamentos de RR. HH."

#. module: hr
#: model:ir.actions.server,name:hr.ir_cron_data_check_work_permit_validity_ir_actions_server
msgid "HR Employee: check work permit validity"
msgstr "Empleado de RR. HH.: comprobar la validez del permiso de trabajo"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/hr_presence_status/hr_presence_status.js:0
#, python-format
msgid "HR Presence Status"
msgstr "Estado de presencia de RR. HH."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "HR Settings"
msgstr "Ajustes de RR. HH."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__has_message
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_message
#: model:ir.model.fields,field_description:hr.field_hr_job__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Highly creative and autonomous"
msgstr "Altamente creativo y autónomo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_hired_employee
msgid "Hired Employees"
msgstr "Empleados contratados"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__home
msgid "Home"
msgstr "Inicio"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__km_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__km_home_work
msgid "Home-Work Distance"
msgstr "Distancia de la casa al trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_icon_display
msgid "Hr Icon Display"
msgstr "Visualización del icono RR. HH."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_res_users__hr_presence_state
msgid "Hr Presence State"
msgstr "Estado de presencia de RR. HH."

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "Recursos humanos"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr "Gerente de recursos humanos"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__id
#: model:ir.model.fields,field_description:hr.field_hr_department__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_employee__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr.field_hr_job__id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__id
msgid "ID"
msgstr "ID"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__id_card
msgid "ID Card Copy"
msgstr "Copia de la tarjeta de identificación"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__barcode
#: model:ir.model.fields,help:hr.field_res_users__barcode
msgid "ID used for employee identification."
msgstr "ID utilizado para identificación del empleado."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
#: model:ir.model.fields,field_description:hr.field_res_users__identification_id
msgid "Identification No"
msgstr "Nº de identificación"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si el campo activo es False, le permitirá ocultar el registro del recurso "
"sin eliminarlo."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__private_car_plate
msgid "If you have more than one car, just separate the plates by a space."
msgstr "Si tiene más de un coche, separe las matrículas con un espacio."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1920
msgid "Image"
msgstr "Imagen"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1024
msgid "Image 1024"
msgstr "Imagen 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_512
msgid "Image 512"
msgstr "Imagen 512"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "Import Template for Employees"
msgstr "Plantilla de importación para empleados"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_interim
msgid "Interim"
msgstr "Interino"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_job__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_manager
msgid "Is Manager"
msgstr "Es un gerente"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__is_system
msgid "Is System"
msgstr "Es un sistema"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Job"
msgstr "Empleo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__description
msgid "Job Description"
msgstr "Descripción del trabajo"

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_id
#: model:ir.model.fields,field_description:hr.field_hr_job__name
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
#: model:ir.ui.menu,name:hr.menu_view_hr_job
msgid "Job Positions"
msgstr "Puestos de trabajo"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Job Summary"
msgstr "Descripción del puesto de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_title
#: model:ir.model.fields,field_description:hr.field_res_users__job_title
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Job Title"
msgstr "Título del trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr "Empleos"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__lang
msgid "Lang"
msgstr "Idioma"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Language"
msgstr "Idioma"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity
msgid "Last Activity"
msgstr "Última actividad"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity_time
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity_time
msgid "Last Activity Time"
msgstr "Último tiempo de actividad"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_date
#: model:ir.model.fields,field_description:hr.field_hr_department__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_date
#: model:ir.model.fields,field_description:hr.field_hr_job__write_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Late Activities"
msgstr "Actividades retrasadas"

#. module: hr
#: model:ir.actions.act_window,name:hr.plan_wizard_action
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Launch Plan"
msgstr "Iniciar plan"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Lead the entire sales cycle"
msgstr "Dirigir todo el ciclo de ventas"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__cohabitant
msgid "Legal Cohabitant"
msgstr "Cohabitante legal"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Let's create a job position."
msgstr "Vamos a crear un puesto de trabajo."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Location"
msgstr "Ubicación"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_number
msgid "Location Number"
msgstr "Número de ubicación"

#. module: hr
#: model:hr.department,name:hr.dep_rd_ltp
msgid "Long Term Projects"
msgstr "Proyectos a largo plazo"

#. module: hr
#: model_terms:hr.job,description:hr.job_hrm
msgid ""
"Lorem Ipsum is simply dummy text of the printing and typesetting industry. "
"Lorem Ipsum has been the industry's standard dummy text ever since the "
"1500s, when an unknown printer took a galley of type and scrambled it to "
"make a type specimen book. It has survived not only five centuries, but also"
" the leap into electronic typesetting, remaining essentially unchanged. It "
"was popularised in the 1960s with the release of Letraset sheets containing "
"Lorem Ipsum passages, and more recently with desktop publishing software "
"like Aldus PageMaker including versions of Lorem Ipsum."
msgstr ""
"Lorem Ipsum es simplemente el texto ficticio de la industria de la impresión"
" y la composición tipográfica. Lorem Ipsum ha sido el texto ficticio "
"estándar de la industria desde el siglo XVI, cuando un impresor desconocido "
"tomó una galera de tipos y la revolvió para hacer un libro de muestras "
"tipográficas. No solo ha sobrevivido durante cinco siglos, sino también al "
"salto a la composición tipográfica electrónica, permaneciendo esencialmente "
"inalterado. Se popularizó en la década de 1960 con el lanzamiento de hojas "
"de Letraset que contenían pasajes de Lorem Ipsum y, más recientemente, con "
"software de autoedición como Aldus PageMaker que incluye versiones de Lorem "
"Ipsum."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__male
msgid "Male"
msgstr "Masculino"

#. module: hr
#: model:hr.department,name:hr.dep_management
msgid "Management"
msgstr "Gestión"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__parent_id
#: model:ir.model.fields,field_description:hr.field_res_users__employee_parent_id
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__manager
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Manager"
msgstr "Gerente"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
#, python-format
msgid "Manager of employee %s is not set."
msgstr "El gerente del empleado %s no está establecido."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__marital
#: model:ir.model.fields,field_description:hr.field_res_users__marital
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Marital Status"
msgstr "Estado civil"

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr "Gerente de marketing y comunicación"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__married
msgid "Married"
msgstr "Casado(a)"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__master
msgid "Master"
msgstr "Master"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__master_department_id
msgid "Master Department"
msgstr "Departamento maestro"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Master demos of our software"
msgstr "Dominar demostraciones de nuestro software"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__member_of_department
msgid "Member of department"
msgstr "Miembro del departamento"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__member_ids
msgid "Members"
msgstr "Miembros"

#. module: hr
#: model:ir.model,name:hr.model_ir_ui_menu
msgid "Menu"
msgstr "Menú"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Must Have"
msgstr "Debe tener"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Department"
msgstr "Mi departamento"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/user_menu/my_profile.js:0
#, python-format
msgid "My Profile"
msgstr "Mi perfil"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Team"
msgstr "Mi equipo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__name
msgid "Name"
msgstr "Nombre"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_id
msgid "Nationality (Country)"
msgstr "Nacionalidad (País)"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Negotiate and contract"
msgstr "Negociar y contratar"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_graph
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_pivot
msgid "New Employees Over Time"
msgstr "Horas extraordinarias de nuevos empleados"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__newly_hired
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__newly_hired
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__newly_hired
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Newly Hired"
msgstr "Recién contratado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Nice to have"
msgstr "Deseable"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_categ_form
msgid "No Tags found ! Let's create one"
msgstr "No se encontraron etiquetas. ¡Creemos una!"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Sin gerentes tontos, sin herramientas estúpidas para usar, sin horarios de "
"trabajo rígidos"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"Sin pérdida de tiempo en los procesos empresariales, responsabilidades "
"reales y autonomía"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Not available"
msgstr "No disponible"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__note
msgid "Note"
msgstr "Nota"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__notes
msgid "Notes"
msgstr "Notas"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
#: model:ir.model.fields,field_description:hr.field_res_users__children
msgid "Number of Dependent Children"
msgstr "Cantidad de hijos dependientes"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr "Número de empleados que actualmente ocupan este puesto."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_hired_employee
msgid ""
"Number of hired employees for this job position during recruitment phase."
msgstr ""
"Número de empleados contratados para este puesto de trabajo durante la fase "
"de reclutamiento."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr "Número de nuevos empleados que espera contratar."

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                    related to employees by departments: expenses, timesheets,\n"
"                    leaves, recruitments, etc."
msgstr ""
"La estructura de departamentos de Odoo se utiliza para administrar todos los documentos.\n"
"                    relacionados con los empleados por departamentos: gastos, partes de horas,\n"
"                    permisos, contrataciones, etc."

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                related to employees by departments: expenses, timesheets,\n"
"                time off, recruitments, etc."
msgstr ""
"La estructura de departamentos de Odoo se utiliza para administrar todos los documentos.\n"
"                relacionados con los empleados por departamentos: gastos, partes de horas,\n"
"                permisos, contrataciones, etc."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__office
msgid "Office"
msgstr "Oficina"

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer: Manage all employees"
msgstr "Encargado: gestionar todos los empleados"

#. module: hr
#: model:ir.actions.act_window,name:hr.mail_activity_plan_action
#: model:ir.ui.menu,name:hr.menu_config_plan_plan
msgid "On/Offboarding Plans"
msgstr "Planes de incorporación y salida"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Open Profile"
msgstr "Abrir perfil"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operación no admitida"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__other
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__other
msgid "Other"
msgstr "Otro"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Our Product"
msgstr "Nuestro producto"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__pin
#: model:ir.model.fields,field_description:hr.field_res_users__pin
msgid "PIN"
msgstr "PIN"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "PIN Code"
msgstr "Código PIN"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__pin
#: model:ir.model.fields,help:hr.field_res_users__pin
msgid ""
"PIN used to Check In/Out in the Kiosk Mode of the Attendance application (if"
" enabled in Configuration) and to change the cashier in the Point of Sale "
"application."
msgstr ""
"PIN que se utiliza para el registro de entrada/salida en el modo quiosco de "
"la aplicación Asistencias (si se habilita en Configuración) y para cambiar "
"el cajero en la aplicación Punto de venta."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_id
msgid "Parent Department"
msgstr "Departamento padre"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_path
msgid "Parent Path"
msgstr "Ruta padre"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_part_time
msgid "Part-Time"
msgstr "Tiempo parcial"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__user_partner_id
msgid "Partner-related data of the user"
msgstr "Datos de usuario relacionados al contacto"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Passion for software products"
msgstr "Pasión por los productos de software"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__passport_id
#: model:ir.model.fields,field_description:hr.field_res_users__passport_id
msgid "Passport No"
msgstr "Número de pasaporte"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Payroll"
msgstr "Nómina"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perfect written English"
msgstr "Inglés escrito perfecto"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perks"
msgstr "Beneficios"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_permanent
msgid "Permanent"
msgstr "Permanente"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Personal Evolution"
msgstr "Evolución personal"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
#, python-format
msgid "Personal information update."
msgstr "Actualización de la información personal."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Phone"
msgstr "Teléfono"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__place_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__place_of_birth
msgid "Place of Birth"
msgstr "Lugar de nacimiento"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plan_ids
msgid "Plan"
msgstr "Plan"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan.py:0
#, python-format
msgid ""
"Plan %(plan_names)s cannot use a department as it is used only for employee "
"plans."
msgstr ""
"El plan %(plan_names)s no puede utilizar un departamento, ya que se utiliza "
"solo para los planes de los empleados."

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan.py:0
#, python-format
msgid ""
"Plan activities %(template_names)s cannot use coach, manager or employee "
"responsible as it is used only for employee plans."
msgstr ""
"Las actividades del plan %(template_names)s no pueden utilizar un mentor, "
"gerente o empleado responsable. Se utiliza solo para los planes de los "
"empleados."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "Plans"
msgstr "Planes"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plans_count
msgid "Plans Count"
msgstr "Número de planes"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr ""
"Practique cualquier deporte con sus compañeros, <br>y nosotros lo pagaremos."

#. module: hr
#: model:ir.model.fields,help:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,help:hr.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Política para enviar un mensaje en el documento usando la pasarela de correo.\n"
"- Todos: cualquiera puede enviar\n"
"- Contactos: sólo contactos autenticados\n"
"- Seguidores: sólo seguidores del documento relacionado o miembros de los siguientes canales\n"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence Control"
msgstr "Control de la asistencia"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence of employees"
msgstr "Presencia de empleados"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence reporting screen, email and IP address control."
msgstr ""
"Control de presencia vía monitoreo de pantalla, correo electrónico y "
"dirección IP."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__present
msgid "Present"
msgstr "Presente"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent_active
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent_active
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent_active
msgid "Present but not active"
msgstr "Presente pero inactivo"

#. module: hr
#: model:ir.actions.report,name:hr.hr_employee_print_badge
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Print Badge"
msgstr "Imprimir credencial"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Address"
msgstr "Dirección privada"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_car_plate
msgid "Private Car Plate"
msgstr "Placa del vehículo privado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_city
#: model:ir.model.fields,field_description:hr.field_res_users__private_city
msgid "Private City"
msgstr "Ciudad privada"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Contact"
msgstr "Contacto privado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_country_id
#: model:ir.model.fields,field_description:hr.field_res_users__private_country_id
msgid "Private Country"
msgstr "País privado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_email
#: model:ir.model.fields,field_description:hr.field_res_users__private_email
msgid "Private Email"
msgstr "Correo electrónico privado"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr "Información privada"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_phone
#: model:ir.model.fields,field_description:hr.field_res_users__private_phone
msgid "Private Phone"
msgstr "Teléfono privado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_state_id
#: model:ir.model.fields,field_description:hr.field_res_users__private_state_id
msgid "Private State"
msgstr "Provincia privada"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_street
#: model:ir.model.fields,field_description:hr.field_res_users__private_street
msgid "Private Street"
msgstr "Calle privada"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_street2
#: model:ir.model.fields,field_description:hr.field_res_users__private_street2
msgid "Private Street2"
msgstr "Calle2 privada"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_zip
#: model:ir.model.fields,field_description:hr.field_res_users__private_zip
msgid "Private Zip"
msgstr "Código Postal privado"

#. module: hr
#: model:hr.department,name:hr.dep_ps
msgid "Professional Services"
msgstr "Servicios profesionales"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_properties
msgid "Properties"
msgstr "Propiedades"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleado público"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Qualify the customer needs"
msgstr "Calificar las necesidades del cliente"

#. module: hr
#: model:hr.department,name:hr.dep_rd_be
msgid "R&D USA"
msgstr "I&D EE. UU."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__rating_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__rating_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Ready to recruit more efficiently?"
msgstr "¿Listo para reclutar más eficazmente?"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""
"Responsabilidades y desafíos reales en una compañía en rápida evolución"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__name
msgid "Reason"
msgstr "Motivo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__reason_code
msgid "Reason Code"
msgstr "Código de motivo"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_recruitment
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr "Reclutamiento"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.act_window,name:hr.hr_departure_wizard_action
#, python-format
msgid "Register Departure"
msgstr "Registrar salida"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
#, python-format
msgid "Related Employees"
msgstr "Empleados relacionados"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr "Usuario relacionado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_ids
msgid "Related employee"
msgstr "Empleado relacionado"

#. module: hr
#: model:ir.model.fields,help:hr.field_res_partner__employee_ids
msgid "Related employees based on their private address"
msgstr "Empleados relacionados en función de su dirección privada"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_id
#: model:ir.model.fields,help:hr.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Usuario relacionado con el recurso para gestionar su acceso."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_homeworking
msgid "Remote Work"
msgstr "Teletrabajo"

#. module: hr
#: model:ir.ui.menu,name:hr.hr_menu_hr_reports
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr "Informes"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__requirements
msgid "Requirements"
msgstr "Requerimientos"

#. module: hr
#: model:hr.department,name:hr.dep_rd
msgid "Research & Development"
msgstr "Investigación y desarrollo"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_resigned
msgid "Resigned"
msgstr "Renunció"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_id
msgid "Resource"
msgstr "Recurso"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_calendar_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_calendar_id
msgid "Resource Calendar"
msgstr "Calendario de recursos"

#. module: hr
#: model:ir.model,name:hr.model_resource_resource
msgid "Resources"
msgstr "Recursos"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Responsibilities"
msgstr "Responsabilidades"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_retired
msgid "Retired"
msgstr "Jubilado/a"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__sinid
msgid "SIN No"
msgstr "Número de seguro social"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__ssnid
#: model:ir.model.fields,field_description:hr.field_res_users__ssnid
msgid "SSN No"
msgstr "Número de seguridad social"

#. module: hr
#: model:hr.department,name:hr.dep_sales
msgid "Sales"
msgstr "Ventas"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Save"
msgstr "Guardar"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Schedule"
msgstr "Horario"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_school
#: model:ir.model.fields,field_description:hr.field_res_users__study_school
msgid "School"
msgstr "Escuela"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_seasonal
msgid "Seasonal"
msgstr "Por temporada"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,help:hr.field_res_users__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""
"Seleccione el “empleado” que instruirá a este empleado.\n"
"El “mentor” no tiene derechos o responsabilidades específicos por defecto."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__sequence
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__sequence
#: model:ir.model.fields,field_description:hr.field_hr_job__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Set default company schedule to manage your employees working time"
msgstr ""
"Establece el horario predeterminado de la compañía para gestionar el tiempo "
"de trabajo de sus empleados"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
msgid "Settings"
msgstr "Ajustes"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__show_hr_icon_display
msgid "Show Hr Icon Display"
msgstr "Mostrar icono de RR. HH."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
#, python-format
msgid "Show employees"
msgstr "Mostrar empleados"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__single
msgid "Single"
msgstr "Soltero(a)"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_skills
msgid "Skills Management"
msgstr "Gestión de habilidades"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__sinid
msgid "Social Insurance Number"
msgstr "Número de seguro social"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__ssnid
#: model:ir.model.fields,help:hr.field_res_users__ssnid
msgid "Social Security Number"
msgstr "Número de seguridad social"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
#, python-format
msgid "Some employee already have a work contact"
msgstr "Algunos empleados ya tienen un contacto de trabajo"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Sport Activity"
msgstr "Actividad deportiva"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_birthdate
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_birthdate
msgid "Spouse Birthdate"
msgstr "Fecha de nacimiento del cónyuge"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_complete_name
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_complete_name
msgid "Spouse Complete Name"
msgstr "Nombre completo del cónyuge"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "State"
msgstr "Provincia"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Status"
msgstr "Estado"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya ha pasado\n"
"Hoy: la fecha límite es hoy\n"
"Planificada: actividades futuras."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Street 2..."
msgstr "Calle 2..."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Street..."
msgstr "Calle..."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Strong analytical skills"
msgstr "Fuertes habilidades analíticas"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__student
msgid "Student"
msgstr "Estudiante"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__name
msgid "Tag Name"
msgstr "Nombre de etiqueta"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_category_name_uniq
msgid "Tag name already exists!"
msgstr "¡El nombre de la etiqueta ya existe!"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__category_ids
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Tags"
msgstr "Etiquetas"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_recruitment
msgid "Target"
msgstr "Objetivo"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Technical Expertise"
msgstr "Conocimientos técnicos"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee_id
msgid "Technical field, bind user to this employee on create"
msgstr "Campo técnico, vincula al usuario con este empleado en la creación"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee
msgid "Technical field, whether to create an employee"
msgstr "Campo técnico, si se debe crear un empleado"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_temporary
msgid "Temporary"
msgstr "Temporal"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_barcode_uniq
msgid ""
"The Badge ID must be unique, this one is already assigned to another "
"employee."
msgstr ""
"El ID de credencial debe ser único, este ya esta asignado a otro empleado."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"EL código ISO del país en dos caracteres.\n"
"Puede utilizar este campo para una búsqueda rápida."

#. module: hr
#: model_terms:hr.job,description:hr.job_marketing
msgid ""
"The Marketing Manager defines the mid- to long-term marketing strategy for his covered market segments in the World.\n"
"              He develops and monitors the annual budget in collaboration with Sales.\n"
"              He defines the products and customers portfolio according to the marketing plan.\n"
"              This mission requires strong collaboration with Technical Service and Sales."
msgstr ""
"El gerente de marketing define la estrategia de marketing a mediano y a largo plazo para sus segmentos cubiertos de mercado en el mundo. \n"
"              Desarrolla y vigila el presupuesto anual en colaboración con el área de Ventas.\n"
"              Define el portafolio de productos y clientes de acuerdo con el plan de marketing.\n"
"              Esta tarea requiere una colaboración sólida con Servicios técnicos y Ventas."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "The PIN must be a sequence of digits."
msgstr "El PIN debe ser numérico."

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
#, python-format
msgid "The employee %s should be linked to a user."
msgstr "El empleado %s debe estar vinculado a un usuario."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__employee_type
#: model:ir.model.fields,help:hr.field_res_users__employee_type
msgid ""
"The employee type. Although the primary purpose may seem to categorize "
"employees, this field has also an impact in the Contract History. Only "
"Employee type is supposed to be under contract and will have a Contract "
"History."
msgstr ""
"El tipo de empleado. Aunque el propósito principal pueda parecer el de "
"categorizar a los empleados, este campo también tiene un impacto en el "
"historial de contratos. Solo el tipo de empleado se supone que está bajo "
"contrato y tendrá un historial de contratos."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_no_of_recruitment_positive
msgid "The expected number of new employees must be positive."
msgstr "El número esperado de nuevos empleados debe ser positivo."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"The fields %r you try to read is not available on the public employee "
"profile."
msgstr ""
"Los campos %r que intenta leer no están disponibles en el perfil de empleado"
" público."

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
#, python-format
msgid "The following fields were modified by %s"
msgstr "Los siguientes campos han sido modificados por %s"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
#, python-format
msgid "The manager of %s should be linked to a user."
msgstr "El gerente de %s debe estar vinculado a un usuario."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_name_company_uniq
msgid "The name of the job position must be unique per department in company!"
msgstr ""
"¡El nombre del puesto de trabajo debe ser único por cada departamento en la "
"empresa!"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
#, python-format
msgid "The user of %s's coach is not set."
msgstr "El usuario del mentor de %s no está establecido."

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "The user will be able to approve document created by employees."
msgstr "El usuario podrá aprobar documentos creados por los empleados."

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid ""
"The user will have access to the human resources configuration as well as "
"statistic reports."
msgstr ""
"El usuario tendrá acceso a la configuración de recursos humanos, así como a "
"los informes estadísticos."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "The work permit of %(employee)s expires at %(date)s."
msgstr "El permiso de trabajo de %(employee)s caduca el %(date)s."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "This employee already has an user."
msgstr "Este empleado ya tiene un usuario."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__tz
#: model:ir.model.fields,help:hr.field_hr_employee_base__tz
#: model:ir.model.fields,help:hr.field_hr_employee_public__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Este campo se utiliza para definir en qué zona horaria trabajarán los "
"recursos."

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
#, python-format
msgid "Those responsible types are limited to Employee plans."
msgstr "Esos tipos de responsables se limitan solo a los planes de empleados."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__to_define
msgid "To Define"
msgstr "A definir"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"To avoid multi company issues (losing the access to your previous contracts,"
" leaves, ...), you should create another employee in the new company "
"instead."
msgstr ""
"Para evitar problemas de multicompañía (perder el acceso a sus contratos "
"anteriores, ausencias, etc.) debe crear otro empleado en la nueva compañía "
"en su lugar."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_to_define
msgid "To define"
msgstr "A definir"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__total_employee
msgid "Total Employee"
msgstr "Total de empleados"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__expected_employees
msgid "Total Forecasted Employees"
msgstr "Total de empleados previstos"

#. module: hr
#: model:hr.job,name:hr.job_trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__trainee
msgid "Trainee"
msgstr "Aprendiz"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Trainings"
msgstr "Formaciones"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción en el registro."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_undetermined
msgid "Undetermined"
msgstr "Indeterminado"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_categ_form
msgid "Use tags to categorize your Employees."
msgstr "Utilice etiquetas para clasificar a sus empleados."

#. module: hr
#: model:ir.model,name:hr.model_res_users
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__user_id
msgid "User"
msgstr "Usuario"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_partner_id
msgid "User's partner"
msgstr "Contacto del usuario"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies:"
msgstr "Ofertas de empleo:"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip_list
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip_list
msgid "Valid IP addresses"
msgstr "Direcciones IP válidas"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Valid work permit for Belgium"
msgstr "Permiso de trabajo válido para Bélgica"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card/avatar_card_popover_patch.xml:0
#, python-format
msgid "View profile"
msgstr "Ver perfil"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_expire
#: model:ir.model.fields,field_description:hr.field_res_users__visa_expire
msgid "Visa Expiration Date"
msgstr "Fecha de expiración del visado"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_no
#: model:ir.model.fields,field_description:hr.field_res_users__visa_no
msgid "Visa No"
msgstr "Número del visado"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "Warning"
msgstr "Advertencia"

#. module: hr
#: model_terms:hr.job,description:hr.job_consultant
msgid ""
"We are currently looking for someone like that to join our Consultant team."
msgstr ""
"Estamos buscando a alguien que quiera unirse a nuestro equipo de "
"consultores. "

#. module: hr
#: model_terms:hr.job,description:hr.job_developer
msgid ""
"We are currently looking for someone like that to join our Web team.\n"
"                Someone who can snap out of coding and perform analysis or meet clients to explain the technical possibilities that can meet their needs."
msgstr ""
"Buscamos a alguien que quiera unirse a nuestro equipo Web.\n"
"                Una persona que vaya más allá de la programación y realice un análisis o se reúna con los clientes para explicar las posibilidades técnicas que puedan cumplir sus necesidades."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What We Offer"
msgstr "Lo que ofrecemos"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What's great in the job?"
msgstr "¿Cuáles son las ventajas del trabajo?"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_public__member_of_department
msgid ""
"Whether the employee is a member of the active user's department or one of "
"it's child department."
msgstr ""
"Si el empleado es un miembro del departamento del usuario activo o de uno de"
" sus departamentos hijos."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__widower
msgid "Widower"
msgstr "Viudo(a)"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                    can easily find all the information you need for each person;\n"
"                    contact data, job position, availability, etc."
msgstr ""
"Con un vistazo rápido a la pantalla de empleado de Odoo,\n"
"                    puede encontrar con facilidad toda la información que necesita de cada persona:\n"
"                    datos de contacto, puesto de trabajo, disponibilidad, etc."

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""
"Con un vistazo rápido a la pantalla de empleados de Odoo\n"
"                puede encontrar con facilidad toda la información que necesita de cada persona:\n"
"                datos de contacto, puesto de trabajo, disponibilidad, etc."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__address_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__address_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_id
msgid "Work Address"
msgstr "Dirección de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_contact_id
#: model:ir.model.fields,field_description:hr.field_res_users__work_contact_id
msgid "Work Contact"
msgstr "Contacto de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_email
#: model:ir.model.fields,field_description:hr.field_res_users__work_email
msgid "Work Email"
msgstr "Correo electrónico de trabajo"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr "Información de trabajo"

#. module: hr
#: model:ir.model,name:hr.model_hr_work_location
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_id
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_form_view
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_tree_view
msgid "Work Location"
msgstr "Ubicación de trabajo"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_work_location_action
#: model:ir.ui.menu,name:hr.menu_hr_work_location_tree
msgid "Work Locations"
msgstr "Ubicación de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__mobile_phone
#: model:ir.model.fields,field_description:hr.field_res_users__mobile_phone
msgid "Work Mobile"
msgstr "Móvil de trabajo"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr "Organización de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_work_permit
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr "Permiso de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_expiration_date
msgid "Work Permit Expiration Date"
msgstr "Fecha de vencimiento del permiso de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr "Número de permiso de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_scheduled_activity
msgid "Work Permit Scheduled Activity"
msgstr "Actividad programada del permiso de trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_phone
#: model:ir.model.fields,field_description:hr.field_res_users__work_phone
msgid "Work Phone"
msgstr "Teléfono del trabajo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_calendar_id
msgid "Working Hours"
msgstr "Horas laborables"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_resource_calendar_view
msgid "Working Schedules"
msgstr "Horarios de trabajo"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
#, python-format
msgid ""
"You are only allowed to update your preferences. Please contact a HR officer"
" to update other information."
msgstr ""
"Sólo puede actualizar sus preferencias. Póngase en contacto con un "
"responsable de RR. HH para actualizar otra información."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/thread_service_patch.js:0
#, python-format
msgid "You can only chat with employees that have a dedicated user."
msgstr "Solo puede chatear con empleados que tengan un usuario dedicado."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_department.py:0
#, python-format
msgid "You cannot create recursive departments."
msgstr "No puede crear departamentos recursivos."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "You do not have access to this document."
msgstr "No tiene acceso a este documento."

#. module: hr
#: model_terms:hr.job,description:hr.job_trainee
msgid ""
"You participate to the update of our tutorial tools and pre-sales tools after the launch of a new version of Odoo. Indeed, any new version of the software brings significant improvements in terms of functionalities, ergonomics and configuration.\n"
"You will have to become familiar with the existing tools (books, class supports, Odoo presentation’s slides, commercial tools),\n"
"to participate to the update of those tools in order to make them appropriate for the new version of the software and, for sure,\n"
"to suggest improvements in order to cover the new domains of the software.\n"
"You join the Implementation Assistance department. This team of 3 people go with Odoo’s clients in the set up of the software. Your role will be\n"
"to animate webinars in order to show the different functionalities of the software.\n"
"to be involved in the support of the customers and\n"
"to answer to their questions.\n"
"You help the support manager to set up new support services by\n"
"being involved in the treatment of new cases,\n"
"contributing to the set up of a new politic,\n"
"being involved into satisfaction surveys in order to have a better knowledge of how the support given is seen by the customers."
msgstr ""
"Participará en la actualización de nuestras herramientas de guía y preventa después del lanzamiento de una nueva versión de Odoo. De hecho, cada nueva versión del software trae mejoras significativas en términos de funciones, ergonomía y configuración.\n"
"Deberá familiarizarse con las herramientas existentes (libros, soportes de clase, diapositivas de presentación de Odoo, herramientas comerciales),\n"
"y participar en la actualización de esas herramientas para adaptarlas a la nueva versión del software y, por supuesto,\n"
" sugerir mejoras para cubrir los nuevos dominios del software.\n"
"Se unirá al departamento de Asistencia en la implementación. Este equipo de 3 personas acompaña a los clientes de Odoo en la configuración del software. Su función será\n"
"participar en los webinars para mostrar las diferentes funciones del software,\n"
"ayudar a los clientes y responder a sus preguntas.\n"
"Ayudará al gerente de soporte a establecer nuevos servicios de soporte al\n"
"participar en el análisis de nuevos casos,\n"
"contribuir a la configuración de una nueva política,\n"
"y participar en el proceso de envío de encuestas de satisfacción para tener un mejor conocimiento de cómo los clientes perciben el soporte brindado."

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_config_settings.py:0
#, python-format
msgid "You should select at least one Advanced Presence Control option."
msgstr ""
"Debe seleccionar al menos una opción de control de asistencia avanzado."

#. module: hr
#: model_terms:hr.job,description:hr.job_cto
msgid ""
"You will take part in the consulting services we provide to our partners and customers: design, analysis, development, testing, project management, support/coaching. You will work autonomously as well as coordinate and supervise small distributed development teams for some projects. Optionally, you will deliver Odoo training sessions to partners and customers (8-10 people/session). You will report to the Head of Professional Services and work closely with all developers and consultants.\n"
"\n"
"The job is located in Grand-Rosière (1367), Belgium (between Louvain-La-Neuve and Namur)."
msgstr ""
"Formará parte de los servicios de consultoría que proporcionamos a nuestros socios y clientes: diseño, análisis, desarrollo, pruebas, gestión de proyectos, soporte/instrucciones. Trabajará de manera autónoma, coordinará y supervisará pequeños equipos de desarrollo distribuidos para algunos proyectos. De manera opcional, impartirá sesiones de formación de Odoo a socios y clientes (8-10 personas por sesión). Su supervisor será el Jefe de servicios profesionales y trabajará con todos los desarrolladores y consultores.\n"
"\n"
"El trabajo se encuentra en Grand-Rosière (1367), Bélgica (entre Louvain-La-Neuve y Namur)."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "ZIP"
msgstr "C.P."

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered employees"
msgstr "direcciones vinculadas a empleados registrados"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr "departamento"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "e.g. John Doe"
msgstr "p. ej. John Doe"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr "p. ej. Gerente de Ventas"

#. module: hr
#. odoo-python
#: code:addons/hr/models/models.py:0
#, python-format
msgid "restricted to employees"
msgstr "restringido a empleados"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_name
msgid "work_permit_name"
msgstr "work_permit_name"
