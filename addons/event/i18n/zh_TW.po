# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event
#
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON>, 2025
# <PERSON>, 2025
#
# "<PERSON> (ngto)" <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2025-08-13 01:10+0000\n"
"Last-Translator: \"<PERSON> (ngto)\" <<EMAIL>>\n"
"Language-Team: Chinese (Traditional Han script) <https://translate.odoo.com/"
"projects/odoo-17/event/zh_Hant/>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__event_count
#: model:ir.model.fields,field_description:event.field_res_users__event_count
msgid "# Events"
msgstr "活動數目"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_count_done
msgid "# Sent"
msgstr "已發送郵件數目"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "%(event_name)s (%(count)s seats remaining)"
msgstr "%(event_name)s (尚餘名額： %(count)s)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "%(event_name)s (Sold out)"
msgstr "%(event_name)s (已售完)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "%(event_name)s - Registration #%(registration_id)s"
msgstr "%(event_name)s - 登記 #%(registration_id)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "%(event_name)s - Registration for %(attendee_name)s"
msgstr "%(event_name)s - %(attendee_name)s 的報名"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "%(ticket_name)s (%(count)s seats remaining)"
msgstr "%(ticket_name)s (尚餘名額： %(count)s)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "%(ticket_name)s (Sold out)"
msgstr "%(ticket_name)s (已售完)"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (副本)"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_attendee_list
msgid "'Attendee List - %s' % (object.name)"
msgstr "'參加者名單 - %s' % (object.name)"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_attendee_list
msgid "'Attendee List'"
msgstr "'參加者名單'"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_badge
msgid ""
"'Badge - %s - %s' % ((object.event_id.name or 'Event').replace('/',''), "
"(object.name or '').replace('/',''))"
msgstr ""
"'識別證 － %s - %s' % ((object.event_id.name or '活動').replace('/',''), "
"(object.name or '').replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_badge
msgid "'Badge - %s' % (object.name or 'Event').replace('/','')"
msgstr "'識別證 － %s' % (object.name or '活動').replace('/','')"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_full_page_ticket
msgid ""
"'Full Page Ticket - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), (object.name or '').replace('/',''))"
msgstr ""
"'整頁門票 - %s - %s' % ((object.event_id.name or '活動').replace('/',''), "
"(object.name or '').replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_full_page_ticket
msgid "'Full Page Ticket - %s' % (object.name or 'Event').replace('/','')"
msgstr "'整頁門票 - %s' % (object.name or '活動').replace('/','')"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "+123456789"
msgstr "+123456789"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "- \"%(event_name)s\": Missing %(nb_too_many)i seats."
msgstr "- 「%(event_name)s」：尚欠 %(nb_too_many)i 座位。"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid ""
"- the ticket \"%(ticket_name)s\" (%(event_name)s): Missing %(nb_too_many)i "
"seats."
msgstr "- 門票「%(ticket_name)s」(%(event_name)s)：尚欠 %(nb_too_many)i 座位。"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_2
msgid "10-14"
msgstr "10-14"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_formatted_event_address
msgid "1000 Brussels"
msgstr "1000 布魯塞爾"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_3
msgid "15-18"
msgstr "15-18"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_4
msgid "18+"
msgstr "18+"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__four_per_sheet
msgid "4 per sheet"
msgstr "每頁 4 個"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_1
msgid "5-10"
msgstr "5-10"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Business Room</b> - To discuss implementation methodologies, best sales "
"practices, etc."
msgstr "<b>商務室</b>- 討論實施方法、最佳銷售實踐等。"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Technical Rooms</b> - One dedicated to advanced Odoo developers, one for "
"new developers."
msgstr "<b>技術室</b>- 一間專供高級 Odoo 開發人員使用，一間供新開發人員使用。"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The Design Fair is preceded by 2 days of Training Sessions for "
"experts!</b><br> We propose 3 different training sessions, 2 days each."
msgstr "<b>設計博覽會之前有 2 天的專家培訓課程！</b><br>我們建議進行 3 次不同的培訓課程，每次 2 天。"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The plenary sessions in the morning will be shorter</b> and we will give "
"more time for thematical meetings, conferences, workshops and tutorial "
"sessions in the afternoon."
msgstr "<b>上午的全體會議將縮短</b>，我們將在下午的專題會議、會議、研討會和輔導課上留出更多時間。"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The whole event is open to all public!</b> <br>We ask a participation fee"
" of 49.50€ for the costs for the 3 days (coffee breaks, catering, drinks and"
" a surprising concert and beer party).<br> For those who don't want to "
"contribute, there is a free ticket, therefore, catering and access to "
"evening events aren't included."
msgstr ""
"<b>整個活動對所有公眾開放！</b> <br>我們要求支付 49.50 歐元的參加費，用於 3 "
"天的費用（茶歇、餐飲、飲料，以及令人驚喜的音樂會和啤酒派對）。<br> 對於不想捐款的人，可選擇免費門票，但不包括餐飲和參加晚間活動的費用。"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<b>Workshop Room</b> - Mainly for developers."
msgstr "<b>工作室</b>- 主要供開發人員使用。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                        <span class=\"text-muted\">Air your tracks online through a Youtube integration</span>"
msgstr ""
"<br/>\n"
"                                        <span class=\"text-muted\">透過 YouTube 整合，線上公開播放你的曲目</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                        <span class=\"text-muted\">Share a quiz to your attendees once a track is over</span>"
msgstr ""
"<br/>\n"
"                                        <span class=\"text-muted\">曲目結束後，立即向參加者分享測驗</span>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<em>If you wish to make a presentation, please send your topic proposal as "
"soon as possible for approval to Mr. Famke Jenssens at ngh (a) yourcompany "
"(dot) com. The presentations should be, for example, a presentation of a "
"community module, a case study, methodology feedback, technical, etc. Each "
"presentation must be in English.</em>"
msgstr ""
"<em>如果您想發表演講，請盡快將您的主題提案發送給 ngh (a) yourcompany (dot) com 的 Famke Jenssens "
"先生以供批准。例如，演示文稿應該是社區模組的演示文稿、案例研究、方法論反饋、技術等。每個演示文稿都必須使用英語。</em>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "<i class=\"fa fa-building\" title=\"Attendee Company\"/>"
msgstr "<i class=\"fa fa-building\" title=\"參加者公司\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Attended button\" "
"title=\"Confirm Attendance\"/>"
msgstr "<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"已參加按鈕\" title=\"確認參加\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirm Registration\"/>"
msgstr "<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"確認按鈕\" title=\"確認報名\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Attended button\" "
"title=\"Confirm Attendance\"/>"
msgstr "<i class=\"fa fa-check\" role=\"img\" aria-label=\"已出席按鈕\" title=\"確認出席\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirm Registration\"/>"
msgstr "<i class=\"fa fa-check\" role=\"img\" aria-label=\"Confirm button\" title=\"確認報名\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-group mt-1 me-2 text-center\" title=\"Attendees\"/>"
msgstr "<i class=\"fa fa-group mt-1 me-2 text-center\" title=\"參加者\"/>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i class=\"fa fa-info-circle me-2\"></i>This event and all the conferences "
"are in <b>English</b>!"
msgstr "<i class=\"fa fa-info-circle me-2\"></i>本次活動及所有會議均以<b>英語</b>進行！"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"箭嘴圖示\" title=\"箭嘴\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-map-marker mt-1 me-2 text-center ps-1\" title=\"Location\"/>"
msgstr "<i class=\"fa fa-map-marker mt-1 me-2 text-center ps-1\" title=\"位置\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "<i class=\"fa fa-ticket\" title=\"Ticket type\"/>"
msgstr "<i class=\"fa fa-ticket\" title=\"門票類型\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-undo fa-3x\" role=\"img\" aria-label=\"Previous state "
"button\" title=\"Previous state\"/>"
msgstr "<i class=\"fa fa-undo fa-3x\" role=\"img\" aria-label=\"前一狀態按鈕\" title=\"前一狀態\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-undo\" role=\"img\" aria-label=\"Previous state button\" "
"title=\"Previous state\"/>"
msgstr "<i class=\"fa fa-undo\" role=\"img\" aria-label=\"前一狀態按鈕\" title=\"前一狀態\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"oi oi-arrow-right me-2 o_event_fontsize_09\" title=\"End date\"/>"
msgstr "<i class=\"oi oi-arrow-right me-2 o_event_fontsize_09\" title=\"結束日期\"/>"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<span class=\"badge text-bg-secondary o_wevent_badge float-"
"end\">SPEAKER</span>"
msgstr ""
"<span class=\"badge text-bg-secondary o_wevent_badge float-end\">演講者</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
msgid "<span class=\"o_event_badge_font_faded\">My Placeholder Company</span>"
msgstr "<span class=\"o_event_badge_font_faded\">我的模擬公司</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Registration\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    註冊\n"
"                                </span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "<span class=\"o_stat_text\">Registration Desk</span>"
msgstr "<span class=\"o_stat_text\">登記櫃台</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">已封存</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<span class=\"text-muted\" invisible=\"state != 'done'\">Attended</span>\n"
"                                    <span class=\"text-muted\" invisible=\"state != 'cancel'\">Canceled</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"state != 'done'\">已出席</span>\n"
"                                    <span class=\"text-muted\" invisible=\"state != 'cancel'\">已取消</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "<span>John Doe</span>"
msgstr "<span>陳大文</span>"

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or 'Guest'\"></span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-attf-href=\"{{ object.event_id.website_url }}\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Event\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-elif=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"></t>,<br>\n"
"                        We are excited to remind you that the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        is starting <strong t-out=\"object.get_date_range_str() or ''\">today</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        We confirm your registration and hope to meet you there,<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>To</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone or ''\"></t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>Get the best mobile experience.</strong>\n"
"                        <a href=\"/event\">Install our mobile app</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address and location\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <i class=\"fa fa-map-marker\"></i>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-attf-src=\"{{ event_address.static_map_url }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\">\n"
"                                <span t-else=\"\">See location on Google Maps</span>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br>\n"
"            Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">你的登記</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or '賓客'\"></span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-attf-href=\"{{ object.event_id.website_url }}\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            查看活動\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-elif=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <t t-out=\"object.name or '賓客'\"></t> 你好！<br>\n"
"                        很高興提醒你，「\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood 系列網上發佈會</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood 系列網上發佈會</strong>\n"
"                        </t>\n"
"                        」活動將於<strong t-out=\"object.get_date_range_str() or ''\">今天</strong>開始。\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>將此活動加入你的日曆：</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal / Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        謹此確認你的登記，希望屆時見到你！<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"object.event_id.name or ''\">OpenWood 系列網上發佈會</t> 團隊\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>開始時間：</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>結束時間：</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\">時區： <i><t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">阿根廷</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">對本次活動有疑問？</span>\n"
"                            <div>請聯絡主辦單位：</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>電郵： <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>電話： <t t-out=\"event_organizer.phone or ''\"></t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>享受最佳的流動裝置體驗：</strong>\n"
"                        <a href=\"/event\">安裝我們的流動應用程式</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address and location\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <i class=\"fa fa-map-marker\"></i>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-attf-src=\"{{ event_address.static_map_url }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google 地圖\">\n"
"                                <span t-else=\"\">在 Google 地圖上查看位置</span>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        由 <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a> 發送\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br>\n"
"            發掘<a href=\"/event\" style=\"color:#875A7B;\">其他精彩活動</a>\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"></t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?badge_mode=1&amp;registration_ids={{ object.ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(object.ids) }}\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                        Download Badge\n"
"                    </a>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"></t>,<br>\n"
"                        Please find attached your badge for\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        See you soon,<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t></div>\n"
"                                <div><strong>From</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>To</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Get the best mobile experience.</strong>\n"
"                            <a href=\"/event\">Install our mobile app</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"></i>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\">\n"
"                                    <t t-else=\"\">See location on Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br>\n"
"                Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: "
"16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; "
"color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\""
"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime("
"object.event_id.date_begin, tz='UTC', "
"dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, "
"tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value="
"\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style="
"\"padding: 16px; background-color: white; color: #454748; border-"
"collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing="
"\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px "
"0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">你的登記</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or '賓客'\"></t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <a t-attf-href=\"/event/{{ object.event_id.id "
"}}/my_tickets?badge_mode=1&amp;registration_ids={{ object.ids "
"}}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(object.ids) "
"}}\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: "
"#FFFFFF; text-decoration: none !important; font-weight: 400; background-"
"color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                        下載名牌\n"
"                    </a>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src="
"\"'/logo.png?company=%s' % object.company_id.id\" style="
"\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt="
"\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing="
"\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px "
"0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <t t-out=\"object.name or '賓客'\"></t> 你好！<br>\n"
"                        附上你的名牌，以供參加\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" "
"style=\"color:#875A7B;text-decoration:none;\" t-out="
"\"object.event_id.name or ''\">OpenWood 系列網上發佈會</a>。\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\""
">OpenWood 系列網上發佈會</strong>。\n"
"                        </t>\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>將此活動新增至你的日曆中</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/"
"render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ "
"date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:"
"3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-"
"radius:3px;\" target=\"new\"><img src="
"\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-"
"align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href="
"\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:"
"1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;"
"\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style"
"=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal / Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/"
"?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name "
"}}&amp;in_loc={{ location }}&amp;st={{ format_datetime("
"object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') "
"}}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='"
"yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid "
"#875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\""
"new\">\n"
"                            <img src="
"\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-"
"align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        期待很快見到你！<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\""
">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"object.event_id.name or ''\">OpenWood "
"系列網上發佈會</t>團隊\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing="
"\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px "
"0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src="
"\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style="
"\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style="
"\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\""
">\n"
"                                <div><t t-out=\"object.event_id.name or ''\""
">OpenWood 系列網上發佈會</t></div>\n"
"                                <div><strong>開始時間：</strong> <t t-out="
"\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 "
"AM</t></div>\n"
"                                <div><strong>結束時間：</strong> <t t-out="
"\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>"
"\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\""
">時區： <i><t t-out=\"object.event_id.date_tz or ''\">Europe/"
"Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src="
"\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style="
"\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style="
"\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>"
"\n"
"                                    <t t-if="
"\"object.event_id.address_id.name\">\n"
"                                        <div t-out="
"\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if="
"\"object.event_id.address_id.street\">\n"
"                                        <div t-out="
"\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value="
"\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if="
"\"object.event_id.address_id.street2\">\n"
"                                        <div t-out="
"\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef="
"\"{{location}}, {{object.event_id.address_id.street2}}\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if="
"\"object.event_id.address_id.city\">\n"
"                                        <t t-out="
"\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef="
"\"{{location}}, {{object.event_id.address_id.city}}\"></t>\n"
"                                    </t>\n"
"                                    <t t-if="
"\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out="
"\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef="
"\"{{location}}, {{object.event_id.address_id.state_id.name}}\"></t>\n"
"                                    </t>\n"
"                                    <t t-if="
"\"object.event_id.address_id.zip\">\n"
"                                        <t t-out="
"\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef="
"\"{{location}}, {{object.event_id.address_id.zip}}\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if="
"\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out="
"\"object.event_id.address_id.country_id.name\">阿根廷</div>\n"
"                                        <t t-set=\"location\" t-valuef="
"\"{{location}}, {{object.event_id.address_id.country_id.name}}\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\""
">對本次活動有疑問？</span>\n"
"                            <div>請聯絡主辦單位：</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\""
">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>電郵： <a t-attf-href="
"\"mailto:{{ event_organizer.email }}\" style=\"text-"
"decoration:none;color:#875A7B;\" t-out=\"event_organizer.email\""
"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>電話： <t t-out="
"\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>享受最佳的流動裝置體驗：</strong>\n"
"                            <a href=\"/event\">安裝我們的流動應用程式</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"></i>\n"
"                                <a t-attf-href="
"\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if="
"\"event_address.static_map_url and event_address.static_map_url_is_valid\" t"
"-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; "
"width: 100%;\" alt=\"Google 地圖\">\n"
"                                    <t t-else=\"\">在 Google "
"地圖上查看位置</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\""
" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; "
"padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            由 <a target=\"_blank\" t-attf-href="
"\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out="
"\"object.company_id.name or ''\">YourCompany</a> 發送\n"
"            <t t-if=\"is_online\">\n"
"                <br>\n"
"                發掘<a href=\"/event\" style=\"color:#875A7B;\""
">其他精彩活動</a>\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or 'Guest'\"></t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <div style=\"margin-bottom: 5px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ object.ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(object.ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Ticket\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-if=\"object.barcode\"> \n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"QR Code\">\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or 'Guest'\"></t>,<br>\n"
"                        We are happy to confirm your registration to the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>.\n"
"                        <t t-if=\"object.partner_id and object.partner_id.name and object.partner_id.name != object.name\">\n"
"                            This ticket was registered by <t t-out=\"object.partner_id.name\"></t>.\n"
"                        </t>\n"
"                    </div>\n"
"                    <div t-if=\"is_sale\">\n"
"                        <br>\n"
"                        The order for this ticket has reference <t t-out=\"object.sale_order_id.name\"></t>\n"
"                        and was placed on <t t-out=\"object.sale_order_id.date_order.date()\"></t>\n"
"                        <t t-if=\"object.sale_order_line_id.price_unit\"> for an amount of\n"
"                            <t t-out=\"object.sale_order_line_id.price_unit\" t-options=\"{'widget': 'monetary', 'display_currency': object.sale_order_line_id.currency_id}\"></t>\n"
"                        </t>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        See you soon,<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t></div>\n"
"                                <div><strong>From</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>To</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i>(<t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t>)</i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Get the best mobile experience.</strong>\n"
"                            <a href=\"/event\">Install our mobile app</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"></i>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\">\n"
"                                    <t t-else=\"\">See location on Google Maps</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br>\n"
"                Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"></t>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"></t>\n"
"<t t-set=\"is_sale\" t-value=\"'sale_order_id' in object and object.sale_order_id\"></t>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"></t>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"></t>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">你的登記</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or '賓客'\"></t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <div style=\"margin-bottom: 5px;\">\n"
"                        <a t-attf-href=\"/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ object.ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(object.ids) }}&amp;responsive_html=1\" target=\"_blank\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            查看門票\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-if=\"object.barcode\"> \n"
"                        <div style=\"margin-bottom: 5px;\">\n"
"                            <img t-attf-src=\"/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0\" width=\"100\" height=\"100\" alt=\"二維碼\">\n"
"                        </div>\n"
"                    </t>\n"
"                    <t t-if=\"not object.company_id.uses_default_logo\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <t t-out=\"object.name or '賓客'\"></t> 你好！<br>\n"
"                        很高興通知你，已確認你報名參加\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood 系列網上發佈會</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood 系列網上發佈會</strong>\n"
"                        </t>。\n"
"                        <t t-if=\"object.partner_id and object.partner_id.name and object.partner_id.name != object.name\">\n"
"                            此門票的註冊人是 <t t-out=\"object.partner_id.name\"></t>。\n"
"                        </t>\n"
"                    </div>\n"
"                    <div t-if=\"is_sale\">\n"
"                        <br>\n"
"                        門票的相關訂單參考編號為 <t t-out=\"object.sale_order_id.name\"></t>，\n"
"                        下單日期在 <t t-out=\"object.sale_order_id.date_order.date()\"></t>\n"
"                        <t t-if=\"object.sale_order_line_id.price_unit\">，金額為 \n"
"                            <t t-out=\"object.sale_order_line_id.price_unit\" t-options=\"{'widget': 'monetary', 'display_currency': object.sale_order_line_id.currency_id}\"></t>\n"
"                        </t>。\n"
"                    </div>\n"
"                    <div>\n"
"                        <br>\n"
"                        <strong>將此活動加入你的日曆：</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> iCal / Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"> Yahoo\n"
"                        </a>\n"
"                        <br><br>\n"
"                    </div>\n"
"                    <div>\n"
"                        期待很快見到你！<br>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"object.event_id.name or ''\">OpenWood 系列網上發佈會</t>團隊\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><t t-out=\"object.event_id.name or ''\">OpenWood 系列網上發佈會</t></div>\n"
"                                <div><strong>開始時間：</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>結束時間：</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\">時區： <i><t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\">\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"></t>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.street2}}\"></t>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.city}}\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name\">C1</t>,\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.state_id.name}}\"></t>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip\">98450</t>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.zip}}\"></t>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name\">阿根廷</div>\n"
"                                        <t t-set=\"location\" t-valuef=\"{{location}}, {{object.event_id.address_id.country_id.name}}\"></t>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">對本次活動有疑問？</span>\n"
"                            <div>請聯絡主辦單位：</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>電郵： <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>電話： <t t-out=\"event_organizer.phone\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>享受最佳的流動裝置體驗：</strong>\n"
"                            <a href=\"/event\">安裝我們的流動應用程式</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address and location\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <i class=\"fa fa-map-marker\"></i>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-if=\"event_address.static_map_url and event_address.static_map_url_is_valid\" t-att-src=\"event_address.static_map_url\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google 地圖\">\n"
"                                    <t t-else=\"\">在 Google 地圖上查看位置</t>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            由 <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a> 發送\n"
"            <t t-if=\"is_online\">\n"
"                <br>\n"
"                發掘<a href=\"/event\" style=\"color:#875A7B;\">其他精彩活動</a>\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__description
#: model:ir.model.fields,help:event.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "您要與客戶溝通的入場券的說明。"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__a4_french_fold
msgid "A4 foldable"
msgstr "A4 可摺疊"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__badge_format__a6
msgid "A6"
msgstr "A6"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__active
#: model:ir.model.fields,field_description:event.field_event_registration__active
msgid "Active"
msgstr "啟用"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_ids
#: model:ir.model.fields,field_description:event.field_event_registration__activity_ids
msgid "Activities"
msgstr "活動"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_2
msgid "Activity"
msgstr "活動"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_state
#: model:ir.model.fields,field_description:event.field_event_registration__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Add a description..."
msgstr "添加描述"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr "將導航選單添加到您的活動網頁，其中包括時間表、追蹤、追蹤建議表格等。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Add some internal notes (to do lists, contact info, ...)"
msgstr "加入一些內部備註（待辦事項清單、聯絡資訊⋯）"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_search
msgid "Address"
msgstr "地址"

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Administrator"
msgstr "管理員"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_exhibitor
msgid "Advanced Sponsors"
msgstr "金牌贊助商"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_sub
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_sub
msgid "After each registration"
msgstr "在完成報名之後"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_event
msgid "After the event"
msgstr "活動之後"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_1
msgid "Age"
msgstr "年齡"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__lang
msgid ""
"All the communication emails sent to attendees will be translated in this "
"language."
msgstr "發送給與會者的所有通訊電郵都將翻譯成這種語言。"

#. module: event
#: model:event.event,name:event.event_6
msgid "An unpublished event"
msgstr "未公開的活動"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"And this time, we go fully ONLINE! Meet us in our live streams from the comfort of your house.<br>\n"
"        Special discount codes will be handed out during the various streams, make sure to be there on time."
msgstr "而這一次，我們全面線上！在您舒適的家中，在我們的直播中與我們見面。<br>特別折扣代碼將在各種流期間分發，請確保准時到達。"

#. module: event
#: model:event.stage,name:event.event_stage_announced
msgid "Announced"
msgstr "已公佈"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "Answers"
msgstr "答案"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Apply change."
msgstr "套用變更。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Archived"
msgstr "已封存"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_launched
msgid "Are sales launched"
msgstr "是否已啟動銷售"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"Around one hundred ballons will simultaneously take flight and turn the sky "
"into a beautiful canvas of colours."
msgstr "大約一百個氣球將同時飛行，將天空變成美麗的彩色畫布。"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "As a team, we are happy to contribute to this event."
msgstr "作為一個團隊，我們很高興為這次活動做出貢獻."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"John DOE年僅13歲，已經開始為客戶開發他的第一個商務應用程式。在掌握了土木工程之後，他創立了TinyERP。這是 OpenERP "
"的第一階段，後來成為 Odoo，全球安裝最多的開源業務軟體。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_attachment_count
#: model:ir.model.fields,field_description:event.field_event_registration__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr "出勤"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__done
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended"
msgstr "參加"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_closed
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended Date"
msgstr "參加日期"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "參加者"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_attendee_list
#: model:ir.actions.report,name:event.action_report_event_registration_attendee_list
msgid "Attendee List"
msgstr "參加者名單"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__name
msgid "Attendee Name"
msgstr "參加者姓名"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Attendee list"
msgstr "參加者名單"

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.actions.act_window,name:event.event_registration_action
#: model:ir.actions.act_window,name:event.event_registration_action_kanban
#: model:ir.model.fields,field_description:event.field_event_event__registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Attendees"
msgstr "參加者"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_available
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_available
msgid "Available Seats"
msgstr "可用席位"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_badge
msgid "Badge"
msgstr "識別證"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_image
msgid "Badge Background"
msgstr "識別證背景"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_format
msgid "Badge Dimension"
msgstr "識別證尺寸"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_badge
msgid "Badge Example"
msgstr "識別證範例"

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
#, python-format
msgid "Badges"
msgstr "識別證"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"Bands like Bar Fighters, Led Slippers and Link Floyd will offer you the show"
" of the century during our three day event."
msgstr "Bar Fighters、Led Slippers 和 Link Floyd 等樂隊將在我們為期三天的活動中為您提供世紀表演。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__barcode
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "Barcode"
msgstr "條碼"

#. module: event
#: model:ir.actions.client,name:event.event_barcode_action_main_view
msgid "Barcode Interface"
msgstr "條碼介面"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "條碼命名規則"

#. module: event
#: model:ir.model.constraint,message:event.constraint_event_registration_barcode_event_uniq
msgid "Barcode should be unique"
msgstr "條碼應該獨一無二"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__before_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__before_event
msgid "Before the event"
msgstr "此活動之前"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_blocked:event.event_stage_announced
#: model:event.stage,legend_blocked:event.event_stage_booked
#: model:event.stage,legend_blocked:event.event_stage_cancelled
#: model:event.stage,legend_blocked:event.event_stage_done
#: model:event.stage,legend_blocked:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__blocked
#, python-format
msgid "Blocked"
msgstr "已封鎖"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_1
msgid ""
"Bloem brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr "Bloem 在幫助客戶處理樹木、花卉和真菌的同時，為木材行業帶來誠實和嚴肅。"

#. module: event
#: model:event.stage,name:event.event_stage_booked
msgid "Booked"
msgstr "已預定"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__partner_id
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Booked by"
msgstr "活動報名者:"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_booth
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Booth Management"
msgstr "攤位管理"

#. module: event
#: model:event.event,subtitle:event.event_5
msgid ""
"Bring your outdoor field hockey season to the next level by taking the field"
" at this 9th annual Field Hockey tournament."
msgstr "參加第 9 屆年度曲棍球錦標賽，讓您的戶外曲棍球賽季更上一層樓。"

#. module: event
#: model:event.event,name:event.event_4
msgid "Business workshops"
msgstr "商業研討會"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Campaign"
msgstr "行銷活動"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel"
msgstr "取消"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Cancel Registration"
msgstr "取消報名"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "Canceled registration"
msgstr "已取消登記"

#. module: event
#: model:event.stage,name:event.event_stage_cancelled
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_id
msgid "Category"
msgstr "類別"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_sequence
msgid "Category Sequence"
msgstr "類別序列"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"Chamber Works reserves the right to cancel, re-name or re-locate the event "
"or change the dates on which it is held."
msgstr "Chamber Works 保留取消、重命名或重新定位活動或更改舉辦日期的權利。"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "Close"
msgstr "關閉"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__color
msgid "Color"
msgstr "顏色"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__color
msgid "Color Index"
msgstr "顏色索引"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Come see us live, we hope to meet you!"
msgstr "歡迎觀看我們的直播，希望見到你！"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr "溝通"

#. module: event
#: model:ir.model.fields,help:event.field_event_mail__mail_registration_ids
msgid "Communication related to event registrations"
msgstr "與活動報名相關的聯繫溝通"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Community Chat Rooms"
msgstr "社區聊天室"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model.fields,field_description:event.field_event_event__company_id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__company_id
#: model:ir.model.fields,field_description:event.field_event_registration__company_id
#: model_terms:event.event,description:event.event_2
#: model_terms:ir.ui.view,arch_db:event.attendee_list
#, python-format
msgid "Company"
msgstr "公司"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
#, python-format
msgid "Company Logo"
msgstr "公司標誌"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__company_name
msgid "Company Name"
msgstr "公司名稱"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Compose Email"
msgstr "撰寫電郵"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_2
msgid "Conference"
msgstr "會議"

#. module: event
#: model:event.event,name:event.event_2
#: model_terms:event.event,description:event.event_2
msgid "Conference for Architects"
msgstr "建築師會議"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Conferences, workshops and trainings will be organized in 6 rooms:"
msgstr "會議、研討會和培訓將在 6 個房間組織："

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr "配置"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "Confirm"
msgstr "確認"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Attendance"
msgstr "確認出席"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Registration"
msgstr "確認登記"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "Confirm attendance for"
msgstr "確認以下參加:"

#. module: event
#: model:ir.model,name:event.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__country_id
msgid "Country"
msgstr "國家"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Create Booths and manage their reservations"
msgstr "建立攤位並管理他們的預訂"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Create an Event"
msgstr "建立活動"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid "Create an Event Stage"
msgstr "建立活動舞台"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Create an Event Tag Category"
msgstr "建立活動標籤類別"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid "Create an Event Template"
msgstr "建立活動模板"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_stage__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_uid
#: model:ir.model.fields,field_description:event.field_event_type__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_date
#: model:ir.model.fields,field_description:event.field_event_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_stage__create_date
#: model:ir.model.fields,field_description:event.field_event_tag__create_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_date
#: model:ir.model.fields,field_description:event.field_event_type__create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_date
msgid "Created on"
msgstr "建立於"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_1
msgid "Culture"
msgstr "文化"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Customer"
msgstr "客戶"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Customer Email"
msgstr "客戶信件"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Date"
msgstr "日期"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__days
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__days
msgid "Days"
msgstr "天"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,help:event.field_event_type_ticket__seats_max
msgid ""
"Define the number of available tickets. If you have too many registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr "定義可用票證的數量。如果您有太多的報名者，您將無法再出售入場券。設置 0 以忽略此設置為無限制的規則。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__description
#: model:ir.model.fields,field_description:event.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event.field_event_type_ticket__description
msgid "Description"
msgstr "說明"

#. module: event
#: model:event.event,name:event.event_0
msgid "Design Fair Los Angeles"
msgstr "洛杉磯設計展"

#. module: event
#: model:event.event,subtitle:event.event_4
msgid "Discover how to grow a sustainable business with our experts."
msgstr "與我們的專家一起探索如何發展可持續發展的業務。"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Discover more"
msgstr "發現更多"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_meet
msgid "Discussion Rooms"
msgstr "討論室"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__display_name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__display_name
#: model:ir.model.fields,field_description:event.field_event_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_stage__display_name
#: model:ir.model.fields,field_description:event.field_event_tag__display_name
#: model:ir.model.fields,field_description:event.field_event_tag_category__display_name
#: model:ir.model.fields,field_description:event.field_event_type__display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Display Sponsors and Exhibitors on your event pages"
msgstr "在您的活動頁面上顯示贊助商和參展商"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_tz
msgid "Display Timezone"
msgstr "顯示時區"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__sequence
msgid "Display order"
msgstr "顯示順序"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__done
msgid "Done"
msgstr "完成"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr "在本次會議期間，我們的團隊將詳細介紹我們的業務模組。您將了解使用它的所有好處。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__email
msgid "Email"
msgstr "電郵"

#. module: event
#: model:ir.model,name:event.model_mail_template
msgid "Email Templates"
msgstr "電郵範本"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Enable barcode scanning"
msgstr "啟用條碼掃瞄"

#. module: event
#: model:ir.model.fields,help:event.field_res_config_settings__use_event_barcode
msgid "Enable or Disable Event Barcode functionality."
msgstr "啟用或停用活動條碼功能。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end
msgid "End Date"
msgstr "結束日期"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end_located
msgid "End Date Located"
msgstr "定位最後日期"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__pipe_end
msgid "End Stage"
msgstr "為結束階段"

#. module: event
#: model:event.stage,name:event.event_stage_done
msgid "Ended"
msgstr "活動結束"

#. module: event
#: model:event.event,subtitle:event.event_2
msgid "Enhance your architectural business and improve professional skills."
msgstr "增強您的建築業務並提高專業技能。"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_event__name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_id
#: model:ir.model.fields,field_description:event.field_event_mail__event_id
#: model:ir.model.fields,field_description:event.field_event_registration__event_id
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
#, python-format
msgid "Event"
msgstr "活動"

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr "自動發送活動信件"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_type_id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__event_type_id
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Event Category"
msgstr "活動類別"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_form
msgid "Event Category Tag"
msgstr "活動類別標籤"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_end_date
msgid "Event End Date"
msgstr "活動結束日期"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Event Gamification"
msgstr "活動遊戲化"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr "活動資訊"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr "活動信件發送排定器"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr "活動信件發送排定器"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_activity
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr "活動名稱"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr "活動組織機構"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_organizer_id
msgid "Event Organizer"
msgstr "活動舉辦單位"

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr "活動報名"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_user_id
msgid "Event Responsible"
msgstr "活動負責人"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid ""
"Event Scheduler for:\n"
"  - Event: %(event_name)s (%(event_id)s)\n"
"  - Scheduled: %(date)s\n"
"  - Template: %(template_name)s (%(template_id)s)\n"
"\n"
"Failed with error:\n"
"  - %(error)s\n"
"\n"
"You receive this email because you are:\n"
"  - the organizer of the event,\n"
"  - or the responsible of the event,\n"
"  - or the last writer of the template.\n"
msgstr ""
"活動調度器：\n"
"  - 活動：%(event_name)s (%(event_id)s)\n"
"  - 預定：%(date)s\n"
"  - 模板：%(template_name)s (%(template_id)s)\n"
"\n"
"失敗，錯誤：\n"
"  - %(error)s\n"
"\n"
"您收到此電子郵件是因為您是：\n"
"  - 活動的組織者，\n"
"  - 或活動的負責人，\n"
"  - 或模板的最後一個作者。\n"

#. module: event
#: model:ir.model,name:event.model_event_stage
msgid "Event Stage"
msgstr "活動階段"

#. module: event
#: model:ir.actions.act_window,name:event.event_stage_action
#: model:ir.ui.menu,name:event.event_stage_menu
msgid "Event Stages"
msgstr "活動階段"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_begin_date
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Start Date"
msgstr "活動開始日期"

#. module: event
#: model:ir.model,name:event.model_event_tag
msgid "Event Tag"
msgstr "活動標籤"

#. module: event
#: model:ir.model,name:event.model_event_tag_category
msgid "Event Tag Category"
msgstr "活動標籤類別"

#. module: event
#: model:ir.actions.act_window,name:event.event_tag_category_action_tree
#: model:ir.ui.menu,name:event.menu_event_category
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_tree
msgid "Event Tags Categories"
msgstr "活動標籤類別"

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type__name
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Template"
msgstr "活動範本"

#. module: event
#: model:ir.model,name:event.model_event_type_ticket
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_form_from_type
msgid "Event Template Ticket"
msgstr "活動範本門票"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_tree_from_type
msgid "Event Template Tickets"
msgstr "活動範本門票"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Event Templates"
msgstr "活動範本"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid ""
"Event Templates combine configurations you use often and are\n"
"                usually based on the types of events you organize (e.g. \"Workshop\",\n"
"                \"Roadshow\", \"Online Webinar\", etc)."
msgstr ""
"活動範本結合了你經常使用的配置，\n"
"                並通常基於你舉辦的活動類型（例如工作坊、\n"
"                路演、線上網路研討會等）。"

#. module: event
#: model:ir.model,name:event.model_event_event_ticket
#: model:ir.model.fields,field_description:event.field_event_event__event_ticket_ids
#: model:ir.model.fields,field_description:event.field_event_registration__event_ticket_id
msgid "Event Ticket"
msgstr "活動門票"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__event_type_id
msgid "Event Type"
msgstr "活動類型"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_tree
msgid "Event registrations"
msgstr "活動報名"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid ""
"Event stages are used to track the progress of an Event from its origin "
"until its conclusion."
msgstr "活動階段用於跟踪活動從起源到結束的進程。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "Event's Ticket"
msgstr "活動的門票"

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
msgid "Event: Mail Scheduler"
msgstr "活動：信件安排工具"

#. module: event
#: model:mail.template,name:event.event_registration_mail_template_badge
msgid "Event: Registration Badge"
msgstr "活動：報名出席證"

#. module: event
#: model:mail.template,name:event.event_subscription
msgid "Event: Registration Confirmation"
msgstr "活動：報名確認"

#. module: event
#: model:mail.template,name:event.event_reminder
msgid "Event: Reminder"
msgstr "活動：提醒"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr "活動"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr "活動信件發送排定器"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_tree
msgid "Events Stage"
msgstr "活動階段"

#. module: event
#: model:ir.model.fields,help:event.field_event_stage__pipe_end
msgid ""
"Events will automatically be moved into this stage when they are finished. "
"The event moved into this stage will automatically be set as green."
msgstr "活動完成後將自動進入此階段。移入此階段的活動將自動設置為綠色。"

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"            This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"每年我們都會邀請我們的社區、合作夥伴和最終用戶前來與我們會面！這是聚在一起展示新功能、未來版本路線圖、軟件成就、研討會、培訓課程等的理想活動⋯\n"
"            本次活動也是展示我們合作夥伴案例研究、方法或發展的機會。在那裡，直接從源頭看到新版本的功能！"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Every year we invite our community, partners and end-users to come and meet "
"us! It's the ideal event to get together and present new features, roadmap "
"of future versions, achievements of the software, workshops, training "
"sessions, etc...."
msgstr ""
"每年我們都會邀請我們的社區、合作夥伴和最終用戶前來與我們會面！這是聚在一起展示新功能、未來版本路線圖、軟件成就、研討會、培訓課程等的理想活動......"

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Exhibition"
msgstr "展覽"

#. module: event
#: model:event.event,subtitle:event.event_3
msgid "Experience live music, local food and beverages."
msgstr "體驗現場音樂、當地美食和飲料。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__fold
msgid "Folded in Kanban"
msgstr "在看板中折疊"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_follower_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_partner_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_type_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
#: model_terms:event.event,description:event.event_7
msgid "For any additional information, please contact us at"
msgstr "若希望取得任何額外的資訊，請和我們取得聯繫"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr "對於每一個活動，您可以定義報名席位的最大值（與會者的人數），高於這個數字的報名將不被接受。"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_1
msgid "For only 10, you gain access to catering. Yum yum."
msgstr "只需 10 個，您就可以獲得餐飲服務。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Foster interactions between attendees by creating virtual conference rooms"
msgstr "通過建立虛擬會議室促進與會者之間的互動"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_0
msgid "Free"
msgstr "免費"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_0
msgid "Free entrance, no food!"
msgstr "免費入場，不提供食物！"

#. module: event
#: model:event.stage,description:event.event_stage_new
msgid "Freshly created"
msgstr "活動初始建立"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_registration
#: model_terms:ir.actions.act_window,help:event.event_registration_action_stats_from_event
msgid ""
"From this dashboard you can report, analyze and detect trends regarding your"
" event registrations."
msgstr "從此儀表板中，您可以報告、分析和檢測有關您的活動報名的趨勢。"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_full_page_ticket
msgid "Full Page Ticket"
msgstr "整頁門票"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_full_page_ticket
msgid "Full Page Ticket Example"
msgstr "整頁門票示例"

#. module: event
#: model:event.stage,description:event.event_stage_done
msgid "Fully ended"
msgstr "已圓滿結束"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr "主要應用的功能流程；"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Future Activities"
msgstr "未來活動"

#. module: event
#: model:event.event.ticket,name:event.event_4_ticket_0
msgid "General Admission"
msgstr "普通門票"

#. module: event
#: model:event.event,subtitle:event.event_0
msgid "Get Inspired • Stay Connected • Have Fun"
msgstr "獲得靈感 • 保持聯繫 • 玩得開心"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_state
msgid "Global communication Status"
msgstr "整體交流狀況"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Google Maps"
msgstr "Google 地圖"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__google_maps_static_api_key
msgid "Google Maps API key"
msgstr "Google 地圖 API 密鑰"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__google_maps_static_api_secret
msgid "Google Maps API secret"
msgstr "Google 地圖 API 秘密"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__use_google_maps_static_api
msgid "Google Maps static API"
msgstr "Google 地圖靜態 API"

#. module: event
#: model:event.event,name:event.event_1
msgid "Great Reno Ballon Race"
msgstr "偉大的里諾氣球比賽"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Great! Now all you have to do is wait for your attendees to show up!"
msgstr "偉大的！現在您所要做的就是等待您的與會者出現！"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_done
msgid "Green Kanban Label"
msgstr "綠色看板標籤"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "灰色看板標籤"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "分組依據"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Happy to be Sponsor"
msgstr "很高興成為贊助商"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__has_message
#: model:ir.model.fields,field_description:event.field_event_registration__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr "已出席本次會議的參加者應能："

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Here it is, the 12th edition of our Live Musical Festival!"
msgstr "這是我們的第 12 屆現場音樂節！"

#. module: event
#: model:event.event,name:event.event_5
msgid "Hockey Tournament"
msgstr "曲棍球錦標賽"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__hours
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__hours
msgid "Hours"
msgstr "小時"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__id
#: model:ir.model.fields,field_description:event.field_event_mail__id
#: model:ir.model.fields,field_description:event.field_event_mail_registration__id
#: model:ir.model.fields,field_description:event.field_event_registration__id
#: model:ir.model.fields,field_description:event.field_event_stage__id
#: model:ir.model.fields,field_description:event.field_event_tag__id
#: model:ir.model.fields,field_description:event.field_event_tag_category__id
#: model:ir.model.fields,field_description:event.field_event_type__id
#: model:ir.model.fields,field_description:event.field_event_type_mail__id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__id
msgid "ID"
msgstr "識別號"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/icon_selection_field/icon_selection_field.js:0
#, python-format
msgid "Icon Selection"
msgstr "圖示選擇"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction
#: model:ir.model.fields,help:event.field_event_registration__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error
#: model:ir.model.fields,help:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__start_sale_datetime
msgid ""
"If ticketing is used, contains the earliest starting sale date of tickets."
msgstr "如果使用票務功能，則包含入場券的最早開始銷售日期。"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "If you don't have this ticket, you will <b>not</b> be allowed entry!"
msgstr "如果您沒有此入場券，您將<b>無法</b>進入！"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"If you don't know anything about Hockey, this is a great introduction to this wonderful sport as you will will be able to see some training process and also have some time\n"
"                to chat with experienced players and trainers once the tournament is over!"
msgstr ""
"如果您對曲棍球一無所知，這是對這項精彩運動的出色介紹，您將能看到一些訓練過程，並且在比賽結束後，\n"
"                還有一些時間與經驗豐富的球員和教練聊天！"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__now
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__now
msgid "Immediately"
msgstr "立即"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Important ticket information"
msgstr "重要票務訊息"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_normal:event.event_stage_announced
#: model:event.stage,legend_normal:event.event_stage_booked
#: model:event.stage,legend_normal:event.event_stage_cancelled
#: model:event.stage,legend_normal:event.event_stage_done
#: model:event.stage,legend_normal:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__normal
#, python-format
msgid "In Progress"
msgstr "進行中"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__date_tz
msgid ""
"Indicates the timezone in which the event dates/times will be displayed on "
"the website."
msgstr "表示網站所顯示的活動日期/時間所屬的時區。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Insert dynamic Google Maps in your email templates"
msgstr "將互動式動態 Google 地圖加入至電郵範本"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_nbr
msgid "Interval"
msgstr "間隔"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr "簡介，CRM，銷售管理"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Invalid event / ticket choice"
msgstr "無效的活動/入場券選擇"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.js:0
#, python-format
msgid "Invalid ticket"
msgstr "門票無效"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__sale_available
msgid "Is Available"
msgstr "可用"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_expired
msgid "Is Expired"
msgstr "已到期"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_finished
msgid "Is Finished"
msgstr "已完成"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_is_follower
#: model:ir.model.fields,field_description:event.field_event_registration__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_one_day
msgid "Is One Day"
msgstr "是單日"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "正在進行"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__seats_max
msgid "It will select this default maximum value when you choose this event"
msgstr "當您選擇這個活動，將選擇預設最大值"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "John DOE"
msgstr "陳大文"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "Join us for the greatest ballon race of all times!"
msgstr "一起參加有史以來最精彩的氣球競賽！"

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid "Join us for this 24 hours Event"
msgstr "加入我們這個 24 小時的活動"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Join us for this 3-day Event"
msgstr "加入我們這個為期 3 天的活動"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "看板阻塞說明"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "看板進展中說明"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state
msgid "Kanban State"
msgstr "看板狀態"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state_label
msgid "Kanban State Label"
msgstr "看板狀態標籤"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_done
msgid "Kanban Valid Explanation"
msgstr "看板有效解釋"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Key"
msgstr "鍵"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__lang
msgid "Language"
msgstr "語言"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Last 30 days"
msgstr "最近30天"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_stage__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_uid
#: model:ir.model.fields,field_description:event.field_event_type__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_date
#: model:ir.model.fields,field_description:event.field_event_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_stage__write_date
#: model:ir.model.fields,field_description:event.field_event_tag__write_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_date
#: model:ir.model.fields,field_description:event.field_event_type__write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Late Activities"
msgstr "逾期活動"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Let's create your first <b>event</b>."
msgstr "讓我們建立您的第一個<b>活動</b>。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_limited
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_limited
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_limited
msgid "Limit Attendees"
msgstr "限制參加人數"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Limit Registrations"
msgstr "限制報名人數"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__has_seats_limitation
msgid "Limited Seats"
msgstr "有限名額"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Live Broadcast"
msgstr "現場直播"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_live
msgid "Live Mode"
msgstr "正式模式"

#. module: event
#: model:event.event,name:event.event_3
msgid "Live Music Festival"
msgstr "現場音樂節"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__notification_type__mail
msgid "Mail"
msgstr "郵件"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_registration_ids
msgid "Mail Registration"
msgstr "信件報名"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type__event_type_mail_ids
msgid "Mail Schedule"
msgstr "信件排程"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduler_id
msgid "Mail Scheduler"
msgstr "信件調度"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr "信件排程器"

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "根據活動分類的信件調度"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__mail_sent
msgid "Mail Sent"
msgstr "已經發送的電郵"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage & publish a schedule with tracks"
msgstr "管理及發佈包含音軌的時間表"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Mark as Attending"
msgstr "標記為確認出席"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Marketing"
msgstr "市場推廣"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
msgid "Maximum"
msgstr "最高數量"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_max
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_max
msgid "Maximum Attendees"
msgstr "與會者最多人數"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__seats_max
msgid "Maximum Registrations"
msgstr "最大報名數"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Maximum Seats"
msgstr "最大座位數"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_medium_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Medium"
msgstr "媒體"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_ids
msgid "Messages"
msgstr "訊息"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__months
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__months
msgid "Months"
msgstr "月"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_2
msgid "Music"
msgstr "音樂"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__my_activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "我的活動"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model:ir.model.fields,field_description:event.field_event_event_ticket__name
#: model:ir.model.fields,field_description:event.field_event_tag__name
#: model:ir.model.fields,field_description:event.field_event_tag_category__name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__name
#: model_terms:ir.ui.view,arch_db:event.attendee_list
#, python-format
msgid "Name"
msgstr "名稱"

#. module: event
#: model:event.stage,name:event.event_stage_new
msgid "New"
msgstr "新增"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_calendar_event_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_summary
#: model:ir.model.fields,field_description:event.field_event_registration__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
msgid "No Attendees expected yet!"
msgstr "還沒有預期的與會者！"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.action_registration
#: model_terms:ir.actions.act_window,help:event.event_registration_action_kanban
#: model_terms:ir.actions.act_window,help:event.event_registration_action_stats_from_event
msgid "No Attendees yet!"
msgstr "還沒有參加者！"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "命名規則"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "None"
msgstr "無"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__note
#: model:ir.model.fields,field_description:event.field_event_type__note
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Note"
msgstr "備註"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Notes"
msgstr "備註"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid "Nothing Scheduled yet!"
msgstr "還沒有規劃！"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Now that your event is ready, click here to move it to another stage."
msgstr "現在您的活動已準備就緒，請點選此處將其移至另一個階段。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_used
msgid "Number of Attendees"
msgstr "參加者數目"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_reserved
msgid "Number of Registrations"
msgstr "報名數目"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_taken
msgid "Number of Taken Seats"
msgstr "已佔用席位數目"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,help:event.field_event_registration__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,help:event.field_event_registration__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Objectives"
msgstr "主題"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "Odoo Community Days"
msgstr "Odoo 社群日"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Once again we assembled the most legendary bands in Rock history."
msgstr "我們再次集結了搖滾史上最具傳奇色彩的樂隊。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ongoing Events"
msgstr "活動進行中"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_1
msgid "Online"
msgstr "網上進行"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Online Exhibitors"
msgstr "線上展商"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_sale
msgid "Online Ticketing"
msgstr "線上購買入場券"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Open date range picker. Pick a Start date for your event"
msgstr "打開日期範圍選擇器。 選擇活動的開始日期"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"OpenElec Applications reserves the right to cancel, re-name or re-locate the"
" event or change the dates on which it is held."
msgstr "OpenElec 模組保留取消、重命名或重新定位活動或更改舉辦日期的權利。"

#. module: event
#: model:event.event,name:event.event_7
msgid "OpenWood Collection Online Reveal"
msgstr "OpenWood 系列網上發佈會"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_2
msgid ""
"OpenWood brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr "OpenWood 在幫助客戶處理樹木、花卉和真菌的同時，為木材行業帶來了誠實和嚴肅。"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "Operation not supported."
msgstr "不支援該操作。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__organizer_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Organizer"
msgstr "舉辦單位"

#. module: event
#: model:event.event,subtitle:event.event_7
msgid ""
"Our newest collection will be revealed online! Interact with us on our live "
"streams!"
msgstr "我們的最新系列將在網上揭曉！在我們的直播中與我們互動！"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_blocked
#: model:ir.model.fields,help:event.field_event_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection."
msgstr "當任務或問題處於該階段時，覆蓋為看板選擇的阻塞狀態顯示的默認值。"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_done
#: model:ir.model.fields,help:event.field_event_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection."
msgstr "當任務或問題處於該階段時，覆蓋為看板選擇的完成狀態顯示的默認值。"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_normal
#: model:ir.model.fields,help:event.field_event_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection."
msgstr "當任務或問題處於該階段時，覆蓋為看板選擇的正常狀態顯示的默認值。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr "參加者"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "業務夥伴"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "Payment"
msgstr "付款"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__phone
msgid "Phone"
msgstr "電話"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Phone number"
msgstr "電話號碼"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid ""
"Please come <b>at least</b> 30 minutes before the beginning of the event."
msgstr "請<b>至少</b>在活動開始前 30 分鐘到達。"

#. module: event
#. odoo-python
#: code:addons/event/models/res_config_settings.py:0
#, python-format
msgid "Please enter a valid base64 secret"
msgstr "請輸入有效的 base64 秘密"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr "銷售點（POS），報告自訂介紹。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Previous state"
msgstr "前一狀態"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "Print"
msgstr "列印"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "Program"
msgstr "計劃"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr "專案管理、人力資源、合約管理。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__registration_properties
msgid "Properties"
msgstr "屬性"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr "採購、銷售和採購管理、財務會計。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
#: model_terms:ir.ui.view,arch_db:event.event_report_template_badge_card
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "QR Code"
msgstr "二維碼"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_quiz
msgid "Quiz on Tracks"
msgstr "專題研討小測驗"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__rating_ids
#: model:ir.model.fields,field_description:event.field_event_registration__rating_ids
msgid "Ratings"
msgstr "評分"

#. module: event
#. odoo-python
#: code:addons/event/models/event_stage.py:0
#: model:event.stage,legend_done:event.event_stage_announced
#: model:event.stage,legend_done:event.event_stage_booked
#: model:event.stage,legend_done:event.event_stage_cancelled
#: model:event.stage,legend_done:event.event_stage_done
#: model:event.stage,legend_done:event.event_stage_new
#, python-format
msgid "Ready for Next Stage"
msgstr "下一階段就緒"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Ready to <b>organize events</b> in a few minutes? Let's get started!"
msgstr "準備好在<b>幾分鐘內組織活動</b>了嗎？讓我們開始吧！"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "紅色的看板標籤"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__open
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registered"
msgstr "已註冊"

#. module: event
#. odoo-javascript
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#: code:addons/event/models/event_ticket.py:0
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#, python-format
msgid "Registration"
msgstr "報名"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Date"
msgstr "報名日期"

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
#: model:ir.ui.menu,name:event.menu_event_registration_desk
#: model:res.groups,name:event.group_event_registration_desk
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#, python-format
msgid "Registration Desk"
msgstr "報名報到處"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__end_sale_datetime
msgid "Registration End"
msgstr "報名結束"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr "報名識別碼"

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "報名信件規劃"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr "報名信件"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__registration_properties_definition
msgid "Registration Properties"
msgstr "註冊屬性"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__start_sale_datetime
msgid "Registration Start"
msgstr "報名開始"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.js:0
#, python-format
msgid "Registration confirmed"
msgstr "登記已確認"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "Registration for %s"
msgstr "報名參加 %s"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr "報名信件"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_open
msgid "Registration open"
msgstr "報名開放"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_stats_from_event
msgid "Registration statistics"
msgstr "報名統計"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__registration_ids
msgid "Registrations"
msgstr "報名"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_open
msgid ""
"Registrations are open if:\n"
"- the event is not ended\n"
"- there are seats available on event\n"
"- the tickets are sellable (if ticketing is used)"
msgstr ""
"報名開放，如果：\n"
"- 活動未結束\n"
"- 活動有座位\n"
"- 入場券可出售（如果使用票務）"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_started
msgid "Registrations started"
msgstr "報名開始"

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr "報告"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_reserved
msgid "Reserved Seats"
msgstr "預訂的席位"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Responsible"
msgstr "負責人"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_user_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_responsive_html_ticket
msgid "Responsive Html Full Page Ticket"
msgstr "回應式 HTML 全頁門票"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_formatted_event_address
msgid "Rue de la Paix 123"
msgstr "Rue de la Paix 123"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__running
msgid "Running"
msgstr "執行中"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
#, python-format
msgid "Scan a badge"
msgstr "掃瞄證件"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr "計劃與追蹤"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__scheduled_date
msgid "Schedule Date"
msgstr "預定日期"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Schedule and organize your events: handle registrations, send automated "
"confirmation emails, sell tickets, etc."
msgstr "安排和組織您的活動：處理報名、發送自動確認電子郵件、售票等。"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__scheduled
msgid "Scheduled"
msgstr "已排程"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduled_date
msgid "Scheduled Time"
msgstr "定期時間"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid "Seasoned Hockey Fans and curious people, this tournament is for you!"
msgstr "經驗豐富的曲棍球迷和好奇的人們，這場比賽適合你們！"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Secret"
msgstr "秘密"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
#, python-format
msgid "Select Attendee"
msgstr "選擇參加者"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr "在你的網站上銷售門票"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr "以銷售訂單售票"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event.field_event_type_mail__notification_type
msgid "Send"
msgstr "發送"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr "以電郵發送"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_done
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__sent
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Sent"
msgstr "發送"

#. module: event
#: model:mail.template,description:event.event_reminder
msgid ""
"Sent automatically to attendees if there is a reminder defined on the event"
msgstr "如果活動定義了提醒，則會自動發送給參加者"

#. module: event
#: model:mail.template,description:event.event_registration_mail_template_badge
msgid "Sent automatically to someone after they registered to an event"
msgstr "在某人報名參加活動後自動發送給他們"

#. module: event
#: model:mail.template,description:event.event_subscription
msgid "Sent to attendees after registering to an event"
msgstr "報名參加活動後發送給參加者"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__sequence
#: model:ir.model.fields,field_description:event.field_event_stage__sequence
#: model:ir.model.fields,field_description:event.field_event_tag__sequence
#: model:ir.model.fields,field_description:event.field_event_tag_category__sequence
#: model:ir.model.fields,field_description:event.field_event_type__sequence
#: model:ir.model.fields,field_description:event.field_event_type_ticket__sequence
msgid "Sequence"
msgstr "序列號"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr "設定"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_4
msgid ""
"Shangai Pterocarpus Furniture brings honesty and seriousness to wood "
"industry while helping customers deal with trees, flowers and fungi."
msgstr "上海紫檀家具在幫助客戶處理樹木、花卉和真菌的同時，為木業帶來誠實和認真。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Show all records which has next action date is before today"
msgstr "顯示在今天之前的下一個行動日期的所有記錄"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_sold_out
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_sold_out
msgid "Sold Out"
msgstr "已售完"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_source_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Source"
msgstr "來源"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_3
#: model:event.type,name:event.event_type_2
msgid "Sport"
msgstr "體育"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__stage_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Stage"
msgstr "階段"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "階段描述和工具提示"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__name
msgid "Stage Name"
msgstr "階段名稱"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__description
msgid "Stage description"
msgstr "活動階段敘述"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_1
#: model:event.event.ticket,name:event.event_2_ticket_1
#: model:event.event.ticket,name:event.event_3_ticket_0
#: model:event.event.ticket,name:event.event_7_ticket_1
msgid "Standard"
msgstr "標準"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Date"
msgstr "開始日期"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin_located
msgid "Start Date Located"
msgstr "定位開始日期"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__start_sale_datetime
msgid "Start sale date"
msgstr "銷售開始日"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__static_map_url
#: model:ir.model.fields,field_description:event.field_res_users__static_map_url
msgid "Static Map Url"
msgstr "靜態地圖網址"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__static_map_url_is_valid
#: model:ir.model.fields,field_description:event.field_res_users__static_map_url_is_valid
msgid "Static Map Url Is Valid"
msgstr "靜態地圖網址有效"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__state
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "狀態"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_state
#: model:ir.model.fields,help:event.field_event_registration__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: event
#: model:ir.model.fields,help:event.field_event_tag__color
msgid ""
"Tag color. No color means no display in kanban or front-end, to distinguish "
"internal tags from public categorization tags."
msgstr "標籤顏色。無顏色意味著看板或前端不顯示，以區分內部標籤和公共分類標籤。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__tag_ids
#: model:ir.model.fields,field_description:event.field_event_tag_category__tag_ids
#: model:ir.model.fields,field_description:event.field_event_type__tag_ids
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "Tags"
msgstr "標籤"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Taken"
msgstr "採取"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_taken
msgid "Taken Seats"
msgstr "已佔用席位"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "任務正在進行中，點選以阻止或設定為完成。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr "任務被阻止。 點選以取消阻止或設定為已完成。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_type_id
#: model:ir.model.fields,field_description:event.field_event_mail__template_ref
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_ref
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Template"
msgstr "範本文字"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__template_model_id
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_model_id
msgid "Template Model"
msgstr "模板模型"

#. module: event
#: model:event.event,subtitle:event.event_1
msgid ""
"The Great Reno Balloon Race is the world's largest free hot-air ballooning "
"event."
msgstr "里諾熱氣球賽是世界上最大的免費熱氣球活動。"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"The best Hockey teams of the country will compete for the national Hockey "
"trophy."
msgstr "該國最好的曲棍球隊將爭奪國家曲棍球獎杯。"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"The best aeronauts of the world will gather on this event to offer you the "
"most spectacular show."
msgstr "全球最優秀的航天員將齊聚本次盛會，為您呈現最精彩的表演。"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "The closing date cannot be earlier than the beginning date."
msgstr "結束日期不能早於開始日期."

#. module: event
#: model:event.stage,description:event.event_stage_cancelled
msgid "The event has been cancelled"
msgstr "活動已取消"

#. module: event
#: model:event.stage,description:event.event_stage_announced
msgid "The event has been publicly announced"
msgstr "該活動已公開宣布"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_sold_out
msgid ""
"The event is sold out if no more seats are available on event. If ticketing "
"is used and all tickets are sold out, the event will be sold out."
msgstr "如果活動中沒有更多座位，則該活動已售罄。如果使用票務功能並且所有入場券已售罄，則該活動將售罄。"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"The finest OpenWood furnitures are coming to your house in a brand new "
"collection"
msgstr "最好的 OpenWood 家具將以全新的系列來到您家"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid ""
"The following tickets cannot be deleted while they have one or more registrations linked to them:\n"
"- %s"
msgstr ""
"以下門票已連結至一項或多項報名註冊，因此無法刪除：\n"
"- %s"

#. module: event
#: model:event.stage,description:event.event_stage_booked
msgid "The place has been reserved"
msgstr "該位置已被預訂"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "The registration must be paid"
msgstr "必須繳納報名費。"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "The safety of our attendees and our aeronauts comes first!"
msgstr "我們的參加者和飛行員的安全是首要考慮！"

#. module: event
#. odoo-python
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid ""
"The stop date cannot be earlier than the start date. Please check ticket "
"%(ticket_name)s"
msgstr "結束日期不能早於開始日期。 請檢查門票 %(ticket_name)s"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid ""
"The template which is referenced should be coming from %(model_name)s model."
msgstr "引用的範本應來自 %(model_name)s 模型。"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "There are not enough seats available for:"
msgstr "沒有足夠座位提供給："

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"This event is also an opportunity to showcase our partners' case studies, "
"methodology or developments. Be there and see directly from the source the "
"features of the version 12!"
msgstr "本次活動也是展示我們合作夥伴案例研究、方法或發展的機會。在那裡直接從源代碼中查看版本 12 的功能！"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"This event is fully online and FREE, if you have paid for tickets, you should get a refund.<br>\n"
"        It will require a good Internet connection to get the best video quality."
msgstr "此活動完全在線且免費，如果您已支付入場券，您應該獲得退款。<br>需要良好的網路連接才能獲得最佳撥放品質。"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__ticket_instructions
#: model:ir.model.fields,help:event.field_event_type__ticket_instructions
msgid "This information will be printed on your tickets."
msgstr "此訊息將列印在您的入場券上。"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This is the <b>name</b> your guests will see when registering."
msgstr "這是您的客人在報名時會看到<b>的名字。</b>"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"This is the perfect place for spending a nice day with your family, we "
"guarantee you will be leaving with beautiful everlasting memories!"
msgstr "這裡是與家人度過美好一天的理想場所，我們保證您將留下美好而恆久的回憶！"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"This is the perfect place for spending a nice time with your friends while "
"listening to some of the most iconic rock songs of all times!"
msgstr "這是與您的朋友共度美好時光的理想場所，同時聆聽一些有史以來最具標誌性的搖滾歌曲！"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "This operator is not supported"
msgstr "不支持此運算符"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "該步驟已完成，點選設定為阻止或進行中。"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "This ticket is for another event"
msgstr "這張門票用於另一場活動"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "This ticket is not for an ongoing event"
msgstr "這張門票不適用於正在進行的活動"

#. module: event
#. odoo-javascript
#. odoo-python
#: code:addons/event/controllers/main.py:0
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
#, python-format
msgid "Ticket"
msgstr "門票"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__ticket_instructions
#: model:ir.model.fields,field_description:event.field_event_type__ticket_instructions
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Ticket Instructions"
msgstr "購票說明"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ticket Type"
msgstr "門票類型"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.attendee_list
msgid "Ticket type"
msgstr "門票類型"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Ticket types allow you to distinguish your attendees. Let's <b>create</b> a "
"new one."
msgstr "入場券類型可讓您區分您的與會者。讓我們<b>建立</b>一個新的。"

#. module: event
#. odoo-python
#: code:addons/event/controllers/main.py:0
#: model:ir.model.fields,field_description:event.field_event_type__event_type_ticket_ids
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_sale
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#, python-format
msgid "Tickets"
msgstr "門票"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Tickets can be printed or scanned directly from your phone."
msgstr "入場券可以直接從您的手機列印或掃描。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_timezone
msgid "Timezone"
msgstr "時區"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Today Activities"
msgstr "今天的活動"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total"
msgstr "總計"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total Attendees"
msgstr "參加者總數"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Total Registrations for this Event"
msgstr "本次活動總報名人數"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track
msgid "Tracks and Agenda"
msgstr "專場和日程"

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Training"
msgstr "培訓"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_3
msgid ""
"Tree Dealers brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr "樹木經銷商在幫助客戶處理樹木、花卉和真菌的同時，為木材行業帶來誠實和嚴肅。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_type
msgid "Trigger"
msgstr "觸發器"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_type
msgid "Trigger "
msgstr "觸發器"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_3
msgid "Type"
msgstr "類型"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__draft
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unconfirmed"
msgstr "未確認"

#. module: event
#: model:ir.model.fields,help:event.field_event_registration__state
msgid ""
"Unconfirmed: registrations in a pending state waiting for an action (specific case, notably with sale status)\n"
"Registered: registrations considered taken by a client\n"
"Attended: registrations for which the attendee attended the event\n"
"Cancelled: registrations cancelled manually"
msgstr ""
"未確認：處於待處理狀態、等待採取行動的註冊（具體情況，特別是銷售狀態）\n"
"已註冊：客戶認為已註冊的註冊\n"
"已參加：參加者參加了活動的註冊\n"
"取消：手動取消的註冊"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid ""
"Under this technical menu you will find all scheduled communication related "
"to your events."
msgstr "在此技術選單下，您將找到與您的活動相關的所有預定交流溝通。"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr "瞭解不同的模組"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_unit
msgid "Unit"
msgstr "單位"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr "今天將開始的活動"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming/Running"
msgstr "即將到來的 或 執行中的"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__use_barcode
msgid "Use Barcode"
msgstr "使用條碼"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__use_event_barcode
msgid "Use Event Barcode"
msgstr "使用活動條碼"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Use Event Tag Categories to classify and organize your event tags."
msgstr "使用活動標籤類別對您的活動標籤進行分類和組織。"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use the <b>breadcrumbs</b> to go back to your kanban overview."
msgstr "使用<b>頁面路徑</b>返回您的看板概覽。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_used
msgid "Used Seats"
msgstr "座位已預定"

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "使用者"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_2
#: model:event.event.ticket,name:event.event_2_ticket_2
#: model:event.event.ticket,name:event.event_3_ticket_1
#: model:event.event.ticket,name:event.event_7_ticket_2
msgid "VIP"
msgstr "VIP"

#. module: event
#. odoo-python
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "Value should be True or False (not %s)"
msgstr "值應為 True 或 False（不是 %s）"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_id
msgid "Venue"
msgstr "場地地點"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_inline
msgid "Venue (formatted for one line uses)"
msgstr "地點（已格式化為一行使用）"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "View"
msgstr "檢視"

#. module: event
#. odoo-python
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid "WARNING: Event Scheduler Error for event: %s"
msgstr "警告: 活動安排存在錯誤: %s"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
#: model_terms:ir.actions.act_window,help:event.event_registration_action_kanban
msgid ""
"Wait until Attendees register to your Event or create their registrations "
"manually."
msgstr "等到與會者報名您的活動或手動建立他們的報名資料。"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr "倉庫管理、製造（MRP）和銷售。匯入/匯出。"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.js:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: event
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid ""
"We reserve the right to cancel, re-name or re-locate the event or change the"
" dates on which it is held in case the weather fails us."
msgstr "我們保留取消、重命名或重新定位活動或更改舉辦日期的權利，以防天氣不佳。"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__website_message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__website_message_ids
#: model:ir.model.fields,help:event.field_event_registration__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__weeks
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__weeks
msgid "Weeks"
msgstr "星期"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
#, python-format
msgid "Welcome to"
msgstr "歡迎來到"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What's new?"
msgstr "有什麼新內容？"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__date_begin
#: model:ir.model.fields,help:event.field_event_registration__event_begin_date
msgid ""
"When the event is scheduled to take place (expressed in your local timezone "
"on the form view)."
msgstr "活動預定的開始時間（在表單檢視畫面中，會以你當地的時區表示）。"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "活動是否開始"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__sale_available
msgid "Whether it is possible to sell these tickets"
msgstr "是否可以出售這些門票"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__is_sold_out
msgid "Whether seats are not available for this ticket."
msgstr "這張門票是否沒有座位。"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_2
msgid "You are truly among the best."
msgstr "你真的是最好的。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr "您還可以添加一個說明來讓您的同時理解相關階段的意思和目的"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels in the kanban view."
msgstr ""
"您可以於此設置活動看板中狀態的意義內容\n"
"                            用來替換預設的活動狀態顏色呈現"

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for {{ object.event_id.name }}"
msgstr "你的 {{ object.event_id.name }} 識別證"

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at {{ object.event_id.name }}"
msgstr "你報名參加 {{ object.event_id.name }} 的登記"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "e.g. \"12-16 years old\""
msgstr "例：12 至 16 歲"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "e.g. \"Age Category\""
msgstr "例：年齡類別"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "e.g. \"Azure Interior\""
msgstr "例如.\"Azure Interior\""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "e.g. \"Promoting\""
msgstr "例：宣傳推廣"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "e.g. Conference for Architects"
msgstr "例：建築師會議"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. How to get to your event, door closing time, ..."
msgstr "例：如何到達你的活動地點、關門時間⋯"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. Online Conferences"
msgstr "例：線上會議"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "e.g. VIP Ticket"
msgstr "例如貴賓票"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "https://www.example.com"
msgstr "https://www.example.com"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "in %d days"
msgstr "在 %d 天內"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "is already registered"
msgstr "已經登記"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_registration_summary_dialog.xml:0
#, python-format
msgid "is successfully registered"
msgstr "已成功登記"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "next month"
msgstr "下個月"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "next week"
msgstr "下周"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "on %(date)s"
msgstr "於 %(date)s"

#. module: event
#. odoo-javascript
#: code:addons/event/static/src/client_action/event_barcode.xml:0
#, python-format
msgid "or"
msgstr "或"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_full_page_ticket_layout
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_started
msgid ""
"registrations have started if the current datetime is after the earliest "
"starting date of tickets."
msgstr "如果當前日期時間晚於入場券的最早開始日期，則報名已開始。"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_kanban_from_event
msgid "reserved"
msgstr "已預留"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "to"
msgstr "到"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "today"
msgstr "今天"

#. module: event
#. odoo-python
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "tomorrow"
msgstr "明天"

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "{{ object.event_id.name }}: {{ object.get_date_range_str() }}"
msgstr "{{ object.event_id.name }}：{{ object.get_date_range_str() }}"
