# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-11 01:02+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
#, python-format
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Añadir direcciones a la lista de Permitidos\n"
"            </p><p>\n"
"                Para protegerle del spam y de las respuestas en bucle, Odoo bloquea automáticamente correos electrónicos\n"
"                que llegan a su puerta de enlace más allá del umbral de <b>%(threshold)i</b> correos electrónicos cada <b>%(minutes)i</b>\n"
"                minutos. En caso de que necesite recibir actualizaciones frecuentes de parte de algunas direcciones\n"
"                puede añadirlas a continuación y Odoo las dejará pasar.\n"
"            </p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr "\" no se sigue más"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr "\"%(activity_name)s: %(summary)s\" asignado a usted"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr "\"%(hostname)s\" necesita acceder a su micrófono"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "\"%(hostname)s\" requires microphone access"
msgstr "\"%(hostname)s\" requiere acceso al micrófono"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr "%(activity)s, asignada a %(name)s, con fecha límite de %(deadline)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
#, python-format
msgid "%(author name)s from %(channel name)s"
msgstr "%(author name)s de %(channel name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
#, python-format
msgid "%(candidateType)s (%(protocol)s)"
msgstr "%(candidateType)s (%(protocol)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""
"%(email)s no se reconoce como un correo electrónico válido. Esto es "
"necesario para crear un nuevo cliente."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%(name)s: %(message)s)"
msgstr "%(name)s: %(message)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"%(new_line)s%(new_line)sType %(bold_start)s@username%(bold_end)s to mention "
"someone, and grab their attention.%(new_line)sType "
"%(bold_start)s#channel%(bold_end)s to mention a channel.%(new_line)sType "
"%(bold_start)s/command%(bold_end)s to execute a command."
msgstr ""
"%(new_line)s%(new_line)sEscriba %(bold_start)s@nombredeusuario%(bold_end)s "
"para mencionar a alguien.%(new_line)sEscriba "
"%(bold_start)s#canal%(bold_end)s para mencionar un canal.%(new_line)sEscriba"
" %(bold_start)s/comando%(bold_end)s para ejecutar un comando."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr ""
"%(open_button)s%(icon)s%(open_em)sDescartar "
"edición%(close_em)s%(close_button)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sESC%(close_samp)s %(open_em)spara "
"%(open_cancel)scancelar%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)spara "
"%(open_save)sguardar%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sESC%(close_samp)s %(open_em)spara "
"%(open_cancel)scancelar%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)spara "
"%(open_save)sguardar%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
#, python-format
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr ""
"%(user)s conectado. Es la primera vez que se conecta, ¡deséeles suerte!"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr "%(user_name)s le invitó a seguir %(document)s el documento: %(title)s"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s le invitó a seguir un nuevo documento."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "%(user_name)s pinned a message to this channel."
msgstr "%(user_name)s fijó un mensaje en este canal."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
#, python-format
msgid "%s (Email Template)"
msgstr "%s (plantilla de correo electrónico)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/out_of_focus_service_patch.js:0
#, python-format
msgid "%s Message"
msgstr "%s mensaje"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/out_of_focus_service_patch.js:0
#, python-format
msgid "%s Messages"
msgstr "%s mensajes"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s y %s están escribiendo.."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr "%s y %s reaccionaron con %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s creado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "%s days overdue"
msgstr "%s días de retraso"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "%s has a request"
msgstr "%s tiene una solicitud"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "%s has a suggestion"
msgstr "%s tiene una propuesta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr "%s reaccionó con %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s esta escribiendo..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
#, python-format
msgid "%s messages found"
msgstr "%s mensajes encontrados"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s raised their hand"
msgstr "%s alzó la mano"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "%s started a live conference"
msgstr "%s inició una conferencia en vivo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s\" requires \"camera\" access"
msgstr "%s\" requiere acceso a la \"cámara\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s\" requires \"screen recording\" access"
msgstr "%s\" requiere acceso a la \"grabación de pantalla\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s y más están escribiendo.."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr "%s, %s, %s y %s otras personas reaccionaron con %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr "%s, %s, %s y 1 persona más reaccionaron con %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr "%s, %s, %s reaccionaron con %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "(Translated from: %(language)s)"
msgstr "(Traducido del: %(language)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "(Translation Failure: %(error)s)"
msgstr "(Falla de traducción: %(error)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "(from"
msgstr "(de"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(originalmente asignado a"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr ". Limite su búsqueda para ver más opciones."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr ""
"<b invisible=\"not no_record\" class=\"text-warning\">Sin registro para este"
" modelo</b>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "<samp>%(send_keybind)s</samp><i> to send</i>"
msgstr "<samp>%(send_keybind)s</samp><i> a enviar</i>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">Color del botón</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">Color del encabezado</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr "<span class=\"me-1 oe_inline\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr "<span class=\"me-1\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr "<span class=\"o_stat_text\">Abrir documento</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr "<span class=\"o_stat_text\">Abrir documento padre</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">Archivado</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                El mensaje se enviará como un correo electrónico a los destinatarios\n"
"                                de la plantilla y no aparecerá en el historial de mensajería.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                El mensaje se publicará como una nota interna visible para los usuarios internos\n"
"                                en el historial de mensajería.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                El mensaje se publicará como mensaje en el registro,\n"
"                                con lo cual se notificará a todos los seguidores. También aparecerá en el historial de mensajería.\n"
"                            </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span name=\"document_followers_text\" invisible=\"not model or "
"composition_mode == 'mass_mail'\">Followers of the document and</span>"
msgstr ""
"<span name=\"document_followers_text\" invisible=\"not model or "
"composition_mode == 'mass_mail'\">Seguidores del documento y</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr "<span>Si usted realizó esta acción:</span><br/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr "<span>Si usted no realizó esta acción:</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr "<span>Abrir registro</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr "<span>Le sugerimos que comience por</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""
"<strong>Comunicación interna</strong>: Respondiendo a este mensaje se "
"publicará una nota interna. Los seguidores externos no recibirán ninguna "
"notificación por correo electrónico."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr "<strong>Nota original:</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>Guarda</strong> esta página y regresa para configurar la función."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr "<strong>Ya no sigue el documento:</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Un diccionario Python que será evaluado para proporcionar valores por "
"defecto al crear nuevos registros para este alias."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "La presencia del bus debe tener un usuario o invitado."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "El miembro de un canal debe ser partner o invitado."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "Un canal del tipo 'chat' no puede tener más de dos usuarios."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "No se debe crear un chat con más de 2 personas. Mejor cree un grupo."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "La reacción a un mensaje debe ser de un partner o de un invitado."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use activities."
msgstr ""
"Solo se puede planificar una siguiente actividad en los modelos que utilizan"
" actividades."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""
"Se requiere una clave API de Google válida para habilitar la traducción de "
"mensajes. https://cloud.google.com/translate/docs/setup"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "Un ajuste de volumen debe tener un partner o un invitado."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Accept"
msgstr "Aceptar"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Access Denied"
msgstr "Acceso denegado"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Grupos de acceso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
#, python-format
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr "Acceso restringido al grupo \"%(groupFullName)s\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Account"
msgstr "Cuenta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "Acción"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Vista de la ventana de acción"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Actions"
msgstr "Acciones"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Las acciones pueden activar comportamientos específicos, como abrir la vista"
" de calendario o marcar automáticamente como hecha al subir un documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "Acciones a realizar en los correos entrantes"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Activado por defecto en la suscripción."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "Activo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "Dominio activo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Activities"
msgstr "Actividades"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Generate"
msgstr "Actividades a generar"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr ""
"Las actividades deben estar vinculadas a registros con un res_id no nulo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Activity"
msgstr "Actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Mixin de actividad"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr "Plan de actividad"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "Planes de actividad"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "Ajustes de la actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Tipo de actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actvidad"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Tipos de actividad"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Plantilla del plan de actividad"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Asistente para planificar actividades"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "Tipo de actividad"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Activity: %s"
msgstr "Actividad: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr "Añadir acción de contexto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "Añadir lista negra de correo electrónico"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Añadir seguidores"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Add Users"
msgstr "Añadir usuarios"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
#, python-format
msgid "Add a Reaction"
msgstr "Añadir una reacción"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr "Añada la clave API de Tenor para habilitar imágenes en formato GIF."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""
"Añada la clave API de Tenor para habilitar imágenes en formato GIF. "
"https://developers.google.com/tenor/guides/quickstart#setup"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "Add a description"
msgstr "Añadir una descripción"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr "Añadir una descripción a su actividad..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr ""
"Añada un %(document)s nuevo o envíe un correo electrónico a %(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Add a new plan"
msgstr "Añadir un nuevo plan"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "Añadir una dirección de correo electrónico a la lista negra"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
#, python-format
msgid "Add as recipient and follower (reason: %s)"
msgstr "Añadir como destinatario y seguidor (motivo: %s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Añadir contactos a notificar..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#, python-format
msgid "Add or join a channel"
msgstr "Añadir o unirse a un canal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr "Añadir firma"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "Añada sus credenciales de twilio a los servidores ICE"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr ""
"No es posible añadir seguidores a los canales, considere añadir miembros."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr ""
"No es posible añadir más miembros a este chat; está diseñado para solo dos "
"personas."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Contactos adicionales"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Avanzado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "Opciones avanzadas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__warning
msgid "Alert"
msgstr "Alerta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "Alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""
"El alias %(matching_name)s (%(current_id)s) ya está vinculado a "
"%(alias_model_name)s (%(matching_id)s) y usado por %(parent_name)s "
"%(parent_model_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""
"El alias %(matching_name)s (%(current_id)s) ya estaba vinculado a "
"%(alias_model_name)s (%(matching_id)s)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "Seguridad de contacto del alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "Dominio de alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "Nombre del dominio de alias"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr "Dominios de alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "Correo electrónico del alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "Nombre del alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "Estado del alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr "Nombre del dominio de alias"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Estado del alias evaluado en el último mensaje recibido."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "Modelo con alias"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr ""
"Los alias %(alias_names)s ya se han usado como dirección de rebote o "
"catchall. Seleccione otro alias."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "Todos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "All Messages"
msgstr "Todos los mensajes"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid "All Messages if not specified"
msgstr "Todos los mensajes si no se especifica"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "All partners must belong to the same message"
msgstr "Todos los contactos deben pertener al mismo mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr "Permitir subida pública"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"Ocurrió una excepción SSL. Compruebe la configuración SSL/TLS en el puerto del servidor.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "Se debe proporcionar un código de acceso para cada archivo adjunto."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr "Se requiere un correo electrónico para que find_or_create funcione"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
#, python-format
msgid "An error occurred when sending an email"
msgstr "Ocurrió un error al enviar un correo electrónico"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr "Ocurrió un error al recuperar los mensajes."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr "Ocurrió un error inesperado durante la creación del chat."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr "Y"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr "Y 1 miembro más."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Aplica a"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#, python-format
msgid "Apply"
msgstr "Aplicar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "Archivado"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr ""
"Archivado porque %(user_name)s (#%(user_id)s) eliminó la cuenta del portal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr "¿Está seguro de que desea eliminar esta plantilla de correo?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr "¿Está seguro de que desea eliminar este mensaje?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""
"¿Está seguro que desea restablecer estas plantillas de correo electrónico a "
"su configuración original? Se perderán los cambios y traducciones."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr ""
"¿Está seguro de que desea eliminar la lista negra de esta dirección de "
"correo electrónico?"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr "Preguntar en el lanzamiento"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Assign to ..."
msgstr "Asignar a..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Assign to me"
msgstr "Asignar a mí"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Assigned To"
msgstr "Asignado a"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
#, python-format
msgid "Assigned to"
msgstr "Asignado a"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""
"El usuario asignado %s no tiene acceso a este documento y no puede ejecutar "
"esta actividad."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Asignación"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "At this point lang should be correctly set"
msgstr ""
"En este punto, el atributo lang debería estar correctamente configurado."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Adjuntar un archivo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Attach files"
msgstr "Adjuntar archivos"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr "Cargando contador de archivos adjuntos..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Audio player:"
msgstr "Reproductor de audio:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Contactos autenticados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Autor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Autor del mensaje. Si no se establece, email_from puede contener una "
"dirección de correo que no coincida con la de ninguna empresa."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Avatar del autor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "Grupo autorizado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Eliminar automáticamente"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "Grupos de suscripción automática"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "Suscripción automática"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Suscripción automática"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr "Notificación automatizada dirigida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Actividad automatizada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Automated message"
msgstr "Mensaje automatizado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"Programe automáticamente esta actividad una vez que la actual se marque como"
" hecha."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Available for all Companies"
msgstr "Disponible para todas las compañías"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr "Avatar del usuario"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "Ausente"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Background blur intensity"
msgstr "Intensidad del desenfoque de fondo"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Base"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr "Plantilla base"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr "Plantillas base"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr "Clave con codificación Base64"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr "Composición del lote"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr ""
"El registro de lotes no admite anexos ni valores de seguimiento en más de 1 "
"documento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Lista negra"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Fecha de la lista negra"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Direcciones incluidas en la lista negra"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "Direcciones de correo electrónico incluidas en la lista negra"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""
"Bloqueado por eliminación de la cuenta del portal %(portal_user_name)s por "
"%(user_name)s (#%(user_id)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Blur video background"
msgstr "Desenfocar el fondo del vídeo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "Cuerpo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr "El contenido del cuerpo es igual al de la plantilla"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Bot"
msgstr "Bot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "Rebotado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr "Alias de rebote"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr "Correo electrónico de rebote"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr ""
"El alias de rebote %(bounce)s que ya se utiliza para otro dominio con el "
"mismo nombre. Utilice otro o simplemente utilice el otro dominio de alias."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr "Los correos electrónicos rebotados deben ser únicos"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""
"Rebote/catchall '%(matching_alias_name)s' ya es usado por %(document_name)s."
" Elija otro alias o cámbielo en el otro documento."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr ""
"Rebote/Catchall '%(matching_alias_name)s' ya se usa. Elija otro alias o "
"cámbielo en el modelo vinculado."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "Rebotado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
#, python-format
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr ""
"Brave: Habilite “Servicios de Google para mensajes push” para activar las "
"notificaciones push."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Browser default"
msgstr "Navegador por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__endpoint
msgid "Browser endpoint"
msgstr "Punto de conexión del navegador "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__keys
msgid "Browser keys"
msgstr "Claves del navegador"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr "CC del correo electrónico"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "CTRL-Enter"
msgstr "CTRL-Enter"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Llamada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Camera is off"
msgstr "La cámara está desactivada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr "Puede cancelar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "Puede editar el cuerpo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr "Puede reenviar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "Puede escribir"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr ""
"No se puede actualizar el mensaje o el destinatario de una notificación."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Cancelar correo electrónico"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "Cancelado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Respuesta predefinida/código corto"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Canned Responses"
msgstr "Respuestas predefinidas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_search
msgid "Canned Responses Search"
msgstr "Búsqueda de respuestas predefinidas"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"Las respuestas predefinidas le permiten insertar mensajes creados previamente en\n"
"                sus mensajes al escribir <i>:acceso rápido</i>. El acceso rápido es\n"
"                remplazado directamente en su mensaje, para que pueda editarlo\n"
"                antes de enviarlo."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr "No se puede cambiar el tipo de canal de: %(channel_names)s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Destinatarios en CC del mensaje"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "Destinatarios en CC"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Destinatarios en CC (se pueden utilizar marcadores de posición aquí)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "Catchall"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr "Alias de catchall"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "Correo electrónico de Catchall"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr ""
"El alias catchall %(catchall)s ya se utiliza para otro dominio con el mismo "
"nombre. Utilice otro catchall o simplemente utilice el otro dominio de "
"alias."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr "Los correos electrónicos catchall deben ser únicos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "Tipo de encadenamiento"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr ""
"Cambie el color de fondo de las actividades relacionadas de este tipo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "Canal"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "Miembro del canal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Tipo de canal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Channel full"
msgstr "Canal lleno"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "Channel members cannot include public users."
msgstr "Los miembros del canal no pueden incluir usuarios públicos."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Channel settings"
msgstr "Configuración de canal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
#, python-format
msgid "Channels"
msgstr "Canales"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr "Canales/Miembros"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "Chat"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"El chat es privado y único entre 2 personas. El grupo es privado entre las "
"personas invitadas. El acceso al canal es libre (dependiendo de su "
"configuración)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Chats"
msgstr "Chats"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr "Verificar lista de ejecución"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Mensajes hijos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr "Elija una plantilla..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr "Elija un usuario..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Choose another value or change it on the other document."
msgstr "Elija otro valor o modifíquelo en el otro documento."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr "Seleccione la asignación para actividades con asignación a petición."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Click here to retry"
msgstr "Haga clic aquí para volver a intentarlo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr "Haga clic para ver los archivos adjuntos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Close Chat Window"
msgstr "Cerrar ventana de chat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Close Search"
msgstr "Cerrar búsqueda"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Close button"
msgstr "Botón de cierre"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
#, python-format
msgid "Close panel"
msgstr "Cerrar panel"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Close search"
msgstr "Cerrar búsqueda"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "Cerrado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr ""
"Recopilar respuestas en una dirección de correo electrónico específica"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Come here often? Install Odoo on your device!"
msgstr "¿Suele visitar esta página? ¡Instale Odoo en su dispositivo!"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Direcciones de destinatarios en CC, separadas por comas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "IDs de destinatarios separados por comas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr "Ids de destinatarios separados por comas (pude usar \"marcadores\")"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Direcciones de destinatarios separadas por comas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Direcciones de destinatarios separadas por comas (se pueden utilizar "
"marcadores de posición aquí)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Comentario"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "Compañías"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr ""
"Empresas que utilizan este dominio como predeterminado para el envío de "
"correos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "Compañía"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Redactar correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Modo de redacción"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "Configuración"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "Configure su lista de servidores ICE para webRTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Configure sus tipos de actividad"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "Configure sus propios servidores de correo electrónico"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#, python-format
msgid "Confirmation"
msgstr "Confirmación"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "Confirmado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr "¡Felicidades! Ha terminado sus actividades."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "¡Enhorabuena! Su bandeja de entrada está vacía."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "¡Enhorabuena! Su bandeja de entrada está vacía."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Error de conexión (problema del servidor de correo de salida)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr "La prueba de conexión ha fallado: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Connection to SFU server closed by the server"
msgstr "Conexión con el servidor SFU cerrada por el servidor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Connection type:"
msgstr "Tipo de conexión:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Connection:"
msgstr "Conexión:"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"Las conexiones son cifradas con SSL/TLS a través de un puerto dedicado (por "
"defecto: IMAPS = 993, POP3S = 995)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "Considere las respuestas como un nuevo hilo"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr "Contacte a su administrador"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Contactos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr "Modelo de contenedor"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr ""
"Incluye la fecha y hora del último evento interesante que ha ocurrido en "
"este canal para este contacto. Esto incluye: crear, unirse, fijar, y nuevo "
"mensaje publicado."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Contenido"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr ""
"Contenido que sustituirá automáticamente el acceso rápido que usted elija. "
"Aún se puede cambiar este contenido antes de enviar su mensaje."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Contenidos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "Conversación en estado plegado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_minimized
msgid "Conversation is minimized"
msgstr "Conversación minimizada"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""
"Contador del número de correos electrónicos rebotados de este contacto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "País"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "Create"
msgstr "Crear"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr "Crear actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "Fecha de Creación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Create Group Chat"
msgstr "Crear chat de grupo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "Crear Uid"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "Crear un nuevo registro"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Create a new Mail Template"
msgstr "Crear una nueva plantilla de correo"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Create a new canned response"
msgstr "Crear una nueva respuesta predefinida"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr "Crear nuevos %(document)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr ""
"Cree un nuevo %(document)s enviando un correo electrónico a %(email_link)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
#, python-format
msgid "Create: #"
msgstr "Crear: #"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Created"
msgstr "Creado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Creado por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "Creado el"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_service_patch.js:0
#, python-format
msgid "Creating a new record..."
msgstr "Creando un nuevo registro..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr "Creador"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "Credencial"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification_web_push
msgid "Cron data used for web push notification"
msgstr "Datos del cron utilizados para la notificación push en el web"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"El usuario actual tiene una notificación destacada asociada a este mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Mensaje rebotado personalizado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr "Lista personalizada de servidores ICE"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr "Plantilla personalizada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr "Plantillas personalizadas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "Nombre de canal personalizado"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr ""
"Se requiere al cliente para recibir notificaciones por correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr "Personalice la apariencia de los correos electrónicos automatizados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr "Notificaciones personalizadas"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "DTLS:"
msgstr "DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Data channel:"
msgstr "Canal de datos:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Fecha"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr "Fecha y hora en la que se debe enviar la notificación."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr "Fecha y hora en la que se fijó el mensaje"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Días"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "Fecha límite"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr "Fecha límite:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Deadline: %s"
msgstr "Fecha límite: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Deafen"
msgstr "Ensordecer"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Estimado"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Dear Sender"
msgstr "Estimado/a remitente"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr "Estimado/a remitente,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Tipo de decoración"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr "Modo de visualización por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr "Desde por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr "Alias Desde por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "Nota por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "Resumen por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Usuario por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "Valores por defecto"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""
"Por defecto de cuando no coincide con los filtros del servidor de salida. "
"Puede ser una parte local, por ejemplo \"notificaciones\" o una dirección de"
" correo electrónico completa, por ejemplo \"<EMAIL>\", "
"para anular todos los correos electrónicos salientes."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Destinatarios por defecto"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Destinatarios por defecto del registro:\n"
"- contacto (usa el id del contacto o partner_id) O\n"
"- correo electrónico (usa email_from o el correo electrónico)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr "Usuario por defecto"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr ""
"Define el orden de procesamiento. Los valores más bajos significan una mayor"
" prioridad."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "Etiqueta de retraso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "Tipo de retraso"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr "Retraso después de activar la función de pulsar para hablar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "Unidades de retraso"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Eliminar correos electrónicos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#, python-format
msgid "Delete all previews"
msgstr "Eliminar todas las vistas previas"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#, python-format
msgid "Delivered"
msgstr "Entregado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Entrega fallida"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr "Fallo en la entrega"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr "Uso obsoleto de 'default_res_id', debería usarse 'default_res_ids'"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Descripción"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Descripción que se añadirá al mensaje mandado para este subtipo. Si no se "
"llena, se pondrá el nombre en su lugar."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"Determina cómo se mostrará por defecto el canal al abrirlo desde su enlace "
"de invitación. Sin valor significa que se mostrará texto (sin voz/vídeo)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#, python-format
msgid "Direct messages"
msgstr "Mensajes directos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr "Desconectar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr "Desconectado de la llamada RTC por el servidor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "Conversaciones"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "Barra lateral de Conversaciones"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr "Conversaciones: activar el sonido de un miembro del canal"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "Canal de conversaciones"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Conversaciones"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Dismiss"
msgstr "Descartar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Muestre una opción en el documento relacionado para abrir un asistente de "
"composición con esta plantilla"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
#, python-format
msgid "Display avatar name"
msgstr "Mostrar nombre de avatar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr "¿Realmente desea eliminar \"%s\"?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#, python-format
msgid "Do you really want to delete this preview?"
msgstr "¿Está seguro de que desea eliminar esta vista previa?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Documento"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Seguidores del documento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr "IDs de documentos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "Modelo de documento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Nombre del documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr "Documento: \""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "Dominio"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#, python-format
msgid "Done"
msgstr "Hecho"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "Listo y lanzar siguiente"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Hecho y programar siguiente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
msgid "Done Date"
msgstr "Fecha de finalización"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Done and Schedule Next"
msgstr "Hecho y programar siguiente"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Download"
msgstr "Descargar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Download Files"
msgstr "Descargar archivos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Download logs"
msgstr "Descargar registros"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Download:"
msgstr "Descargar:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/dropzone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr "Arrastre los archivos aquí"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "Fecha de vencimiento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Fecha de vencimiento en"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Due in"
msgstr "Vence en"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Due in %s days"
msgstr "Vence en %s días"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Due on"
msgstr "Vence el"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Tipo de vencimiento"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Correo electrónico duplicado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "Duración de la actividad vocal en el mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "Informes dinámicos"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr "Usuario dinámico (según el registro)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Edge blur intensity"
msgstr "Intensidad del desenfoque de los bordes"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Editar contactos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
#, python-format
msgid "Edit Subscription of %(name)s"
msgstr "Editar suscripción de %(name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "Edit subscription"
msgstr "Editar suscripción"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
#, python-format
msgid "Edit: %s"
msgstr "Editar: %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "Correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr "Añadir firma de correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "Dirección de correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "Alias de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Alias de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "Mixin de alias de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr "Mixin de alias de correo electrónico (claro)"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "Lista negra de correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr "Color del botón del correo electrónico "

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "Gestión del CC del correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Configuración del correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr "Dominio de correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr "Color del encabezado del correo electrónico"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "Envío masivo de correos electrónicos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr "Diseño de notificación de correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "Vista previa del correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Búsqueda por correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "Plantilla de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "Vista previa de la plantilla del correo electrónico"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "Plantillas de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Hilo de correo electrónico"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "¡Esta dirección de correo electrónico ya existe!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Dirección de correo del remitente. Este campo se establece cuando no se "
"encuentra ningún contacto que coincida y reemplaza el campo author_id en el "
"chatter."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr ""
"Dirección de correo electrónico a la que se redireccionarán las respuestas "
"cuando se envíen correos electrónicos masivos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"Dirección de correo electrónico a la que se redireccionarán las respuestas "
"cuando se envíen correos electrónicos masivos; solo se utiliza cuando la "
"respuesta no está registrada en el hilo de conversación original."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr ""
"Las direcciones de correo electrónico que estén en la lista negra no "
"recibirán más correos electrónicos"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr ""
"Los alias de correo electrónico %(alias_name)s no pueden usarse en varios "
"registros al mismo tiempo. Actualice los registros uno por uno."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "CC del correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Asistente de redacción de correo electrónico"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""
"Dominio de correo electrónico, por ejemplo \"ejemplo.com\" en "
"\"<EMAIL>\"."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Mensaje de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "Asistente de reenvío de correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "Plantillas de correo electrónico"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "Correos electrónicos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid "Emoji"
msgstr "Emoji"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
#, python-format
msgid "Emojis"
msgstr "Emojis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "Empleado solamente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "Activar el seguimiento de pedidos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Enable Push-to-talk"
msgstr "Habilitar pulsar para hablar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Enable desktop notifications to chat"
msgstr "Activar notificaciones de escritorio para el chat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Enter"
msgstr "Enter"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Enter Full Screen"
msgstr "Acceder a la pantalla completa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "Error"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "Mensaje de error"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Error durante la comunicación con el servidor de garantía del editor."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "Mensaje de error"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"Error sin excepción. Probablemente debido a la actualización de acceso "
"concurrente de los registros de notificación. Por favor, consulte con un "
"administrador."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr ""
"Error sin excepción. Probablemente debido al enviar un correo sin los "
"recipientes procesados."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Error, una empresa no puede seguir dos veces el mismo objeto."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "Todos"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Excepción"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Exit Full Screen"
msgstr "Salir de pantalla completa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Expand"
msgstr "Expandir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__expiration_time
msgid "Expiration Token Date"
msgstr "Fecha de expiración del token"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Filtros avanzados..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Correo fallido"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Fallido"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
#, python-format
msgid "Failed to enable push notifications"
msgstr "Ocurrió un error al habilitar las notificaciones push"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Failed to load gifs..."
msgstr "Error al cargar gifs..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr ""
"No se pudo cargar el servidor SFU, se está utilizando la red de pares como "
"alternativa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""
"Error al visualizar la plantilla QWeb: %(template_src)s\n"
"\n"
"%(template_traceback)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr "Error al visualizar la plantilla inline_template: %(template_txt)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template: %(view_ref)s"
msgstr "Error al visualizar la plantilla: %(view_ref)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Razón del fallo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "Razón del fallo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Razón del error. Por lo general es la excepción lanzada por el servidor de "
"correo electrónico y se almacena para facilitar la eliminación de errores."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Tipo de fallo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Favorito de"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Favorites"
msgstr "Favoritos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "Buscar ahora"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr "Busque hasta el número especificado de GIF."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "Campo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "El campo \"Actividad de correo\" no se puede cambiar a \"False\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "El campo \"Lista negra de correo\" no se puede cambiar a \"False\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "El campo \"Hilo de mensajes\" no puede cambiarse a \"False\"."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr "Grupos del campo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "Detalles del campo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Campo usado para enlazar el modelo relacionado al modelo de subtipo cuando "
"se utiliza suscripción automática en el documento relacionado. El campo se "
"usa para calcular getattr(relacionado.campo_relacionado)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
#, python-format
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""
"El campo \"%(field)s\" en el modelo \"%(model)s\" debe ser del tipo Many2one"
" y debe tener tracking=True para el cálculo de la duración."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Campos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr ""
"Archivo desde el que se origina la plantilla. Se utiliza para restablecer la"
" plantilla rota."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
#, python-format
msgid "File too large"
msgstr "Archivo demasiado grande"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "File upload is disabled for external users"
msgstr "La subida de archivos está desactivada para los usuarios externos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "File upload is enabled for external users"
msgstr "La subida de archivos está habilitada para usuarios externos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Files"
msgstr "Archivos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Fold"
msgstr "Plegar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "Plegado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Follow"
msgstr "Seguir"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "Seguidores"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Formulario de seguidores"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Sólo seguidores"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr "Seguidores a añadir"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr "Seguidores a eliminar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "Following"
msgstr "Siguiendo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome p. ej. fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr ""
"Para %(channels)s, el channel_type debe ser 'canal' para tener la "
"autorización basada en el grupo o la autosuscripción del grupo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 1 hour"
msgstr "Por 1 hora"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 15 minutes"
msgstr "Por 15 minutos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 24 hours"
msgstr "Por 24 horas"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 3 hours"
msgstr "Por 3 horas"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 8 hours"
msgstr "Por 8 horas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr "Forzar envío"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr "Forzar un idioma:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "Correo electrónico formateado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "De"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "From peer:"
msgstr "De colega:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "Compositor completo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "Vídeo en pantalla completa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Future"
msgstr "Futuro"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF"
msgstr "GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF Category"
msgstr "Categoría de GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF Favorites"
msgstr "GIFs favoritos"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr "GIF favorito "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr "ID de GIF de Tenor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid "GIFs"
msgstr "GIFs"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "Puerta de enlace"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Go to conversation"
msgstr "Ir a la conversación "

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr "Integración con Google Translate"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "Grupo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Agrupar por"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "Nombre del grupo"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr ""
"La autorización y autosuscripción de grupos solo es posible en canales."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Agrupar por..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr "Chat grupal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "Grupos"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
#, python-format
msgid "Guest"
msgstr "Invitado"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr "El campo de nombre de invitado no puede estar vacío."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr "El nombre del invitado es demasiado largo."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "Clientes"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP "

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Gestionar por correo electrónico"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Gestionar en Odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr "Tiene un error"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr "Tiene actividad de correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr "Tiene lista negra de correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr "Tiene un hilo de correos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Tiene menciones"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "Ha desactivado el sonido de entrada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Tiene un error"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr "Tiene responsables bajo demanda"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Encabezados"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "Hola"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "Hola, "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Oculto"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr "Plantilla oculta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Hide Attachments"
msgstr "Ocultar archivos adjuntos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Hide Call Settings"
msgstr "Ocultar los ajustes de llamada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Hide Member List"
msgstr "Ocultar lista de miembros"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#, python-format
msgid "Hide Pinned Messages"
msgstr "Ocultar los mensajes fijados"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
#, python-format
msgid "Hide sidebar"
msgstr "Ocultar barra lateral"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Ocultar el subtipo en las opciones del seguidor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Ocultar a los usuarios públicos o del portal, independientemente de la "
"configuración del subtipo."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "Alta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "History"
msgstr "Historial"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "Nombre del host o IP del servidor de correo"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""
"Cuánto tiempo permanece activa la emisión de audio tras superar el límite de"
" volumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "Servidores ICE"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "ICE gathering:"
msgstr "Recolección ICE:"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "Servidor ICE"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr "Servidores ICE"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "ICE:"
msgstr "ICE:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__id
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID del registro padre que tiene el alias. (ejemplo: el proyecto que contiene"
" el alias para la creación de tareas)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "Estado de mensajería instantanea"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "Servidor IMAP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id del recurso seguido"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr "Identidad"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Idle"
msgstr "Inactivo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "Si se requiere SSL."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "If not set, shared with all users."
msgstr "Si no se establece, se comparte con todos los usuarios."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr ""
"Si se establece, se realiza un seguimiento de cada modificación realizada en"
" este campo. El valor se utiliza para ordenar los valores de seguimiento."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr ""
"Si se establece, el miembro no recibirá notificaciones del canal hasta esa "
"fecha."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"Si se establece, el gerente de tareas enviará el correo electrónico después "
"de la fecha. Si se establece, el correo electrónico se enviará lo antes "
"posible. A menos que se especifique una zona horaria, se considerará que "
"está en la zona horaria UTC."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr ""
"Si esta opción está activada, el gerente de colas enviará el correo "
"electrónico después de la fecha. Si no está activada, el correo electrónico "
"se enviará lo antes posible. Puede utilizar expresiones dinámicas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Si se configura, este contenido se enviará automáticamente a usuarios no "
"autorizados en lugar del mensaje por defecto."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""
"Si esta opción está activada, restringirá la plantilla a este usuario "
"específico.                                                   Si no está "
"activada, se comparte con todos los usuarios."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Si la dirección de correo electrónico esta en la lista negra, el contacto ya"
" no recibirá correo masivo de cualquier lista."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"Si está marcado como True, las respuestas no se mostrarán en el hilo de "
"conversación del documento original. En su lugar, se comprobará el reply_to "
"en el seguimiento del message-id y se redireccionará como corresponde. Esto "
"tiene un impacto en el message-id generado."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Si tiene un dominio de correo electrónico general redireccionado a un "
"servidor de Odoo, ponga el nombre del dominio aquí."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "Si desea utilizar twilio como proveedor de servidores TURN/STUN"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr "Ignorar todo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "Imagen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "Imagen 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "Imagen 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr "Tipo de MIME de la imagen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "La imagen es un enlace"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr ""
"En modo comentario: si se establece, pospone el envío de notificaciones. En "
"el modo de correo masivo: si se establece, envía los correos electrónicos "
"después de esa fecha. Esta fecha se considera en la zona horaria UTC."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "Alias inactivo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "Servidor de correo de entrada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "Bandeja de entrada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Incoming Call..."
msgstr "Llamada entrante..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr "Correo electrónico entrante"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "Servidores de correo de entrada"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "Servidor de correo de entrada"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "Servidores de correo de entrada"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""
"Indica que esta actividad ha sido creada automáticamente y no por ningún "
"usuario."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Info"
msgstr "Información"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "Modelo inicial"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr "Dirección completa Inline"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Input device"
msgstr "Dispositivo de entrada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Install"
msgstr "Instalar"

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "Integraciones"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Solo interno"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr "Comunicación interna:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr "No válido"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Invalid domain %(domain)r (type %(domain_type)s)"
msgstr "Dominio inválido %(domain)r (tipo %(domain_type)s)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Dirección de correo electrónico no válida"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "Dirección de correo electrónico no válida %r"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Expresión invalida, debe ser un diccionario python, por ejemplo \"{'field': "
"'value'}\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr "Campo \"%(field_name)s\" no válido al crear un canal con miembros."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr "No válido desde la dirección"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "Campo de correo electrónico primario no válido en el modelo %s"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
#, python-format
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr "res_ids %(res_ids_str)s no válidos (tipo %(res_ids_type)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""
"Nombre de servidor no válido.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr ""
"Plantilla u origen de vista no válido %(svalue)s (tipo %(stype)s), debería "
"ser un registro o un XMLID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr ""
"Plantilla no válido o ID de Xml del origen de vista %(source_ref)s ya no "
"existe"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr ""
"Plantilla o registro de origen de vista no válido %(svalue)s, es %(model)s "
"por el contrario"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr ""
"Plantilla o referencia de origen de vista no válido %(svalue)s, es %(model)s"
" por el contrario"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""
"Valor no válido al crear un canal con miembros, solo se permiten 4 o 6"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr "Valor no válido al crear un canal con membresías, solo se permite 0."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr "URL de invitación"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "Invitación para seguir %(document_model)s: %(document_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite"
msgstr "Invitar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
#, python-format
msgid "Invite Follower"
msgstr "Invitar a seguidor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Invite a User"
msgstr "Invitar a usuario"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite people"
msgstr "Invitar gente"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite to Channel"
msgstr "Invitar a canal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite to Group Chat"
msgstr "Invitar al chat de grupo"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Asistente de invitación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "Está Activo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "Es un usuario o autor invitado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr "Es editable"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "Es un editor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr "Es miembro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Es leído"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr "Es uno mismo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr "Es editor de plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_chat
msgid "Is a chat"
msgstr "Es un chat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr "Es un registro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr ""
"¿Está abierto el canal de la categoría de la barra lateral de "
"conversaciones?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr ""
"¿Está abierto el chat de la categoría de la barra lateral de conversaciones?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "Está silenciado el micrófono"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr "Está fijada en la interfaz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "Está enviando un vídeo de usuario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "Está compartiendo pantalla"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr ""
"Parece que está intentando crear un miembro de canal, pero parece que ha "
"olvidado especificar el canal relacionado. Si desea continuar, asegúrese de "
"proporcionar la información necesaria sobre el canal."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_partner_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""
"Se refiere a las claves de navegador que la notificación utiliza: \n"
"- p256dh: es la clave pública de suscripción que el navegador genera. El navegador mantendrá \n"
"          la clave privada como secreto y la usará para desencriptar la carga efectiva.\n"
"- auth: el valor auth debe tratarse como secreto y no se debe compartir fuera de Odoo."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON que mapea IDs de un campo many2one a segundos utilizados"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "Unirse"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Join Call"
msgstr "Unirse a la llamada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Join Channel"
msgstr "Unirse al canal"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "Unirse al grupo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#, python-format
msgid "Jump"
msgstr "Ir"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Jump to Present"
msgstr "Ir a la presentación actual"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "Guardar archivos adjuntos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr "Mantener actividades hechas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr "Conservar copia del mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "Mantener el original"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr ""
"Conserve una copia del contenido del correo electrónico si estos son "
"eliminados (sólo envíos masivos)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr ""
"Mantener las actividades marcadas como hechas en la vista de actividades"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "Clave"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
#, python-format
msgid "Kind Regards"
msgstr "Saludos cordiales,"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr "EN VIVO"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Idioma"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "Última fecha de búsqueda"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "Recuperado por última vez"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr "Último interés"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "Visto por última vez"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "Última actualización el"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__last_used
msgid "Last Used"
msgstr "Último uso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr "Fecha de la última visita"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__last_used
msgid "Last time this shortcode was used"
msgstr "Última vez que se utilizo este código corto "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Late"
msgstr "Retrasado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Actividades retrasadas"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Launch Plans"
msgstr "Lanzar planes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Diseño"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "Abandonar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid "Leave Conversation"
msgstr "Abandonar conversación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Leave this channel"
msgstr "Abandonar este canal"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr "Vistas previas de enlaces"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Link copied!"
msgstr "¡Enlace copiado!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#, python-format
msgid "List users in the current channel"
msgstr "Lista actual de usuarios en este canal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Load More"
msgstr "Cargar más"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Load more"
msgstr "Cargar mas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Load template"
msgstr "Cargar plantilla"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "Loading"
msgstr "Cargando"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "Servidor local"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Detección de entradas según las partes locales"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr ""
"Parte local del correo electrónico que se utiliza como \"responder a\" para "
"recibir respuestas, por ejemplo, \"catchall\" en \"<EMAIL>\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""
"Parte local del correo electrónico que se utiliza como \"ruta de "
"devolución\" en caso de que los correos se rebotan, por ejemplo, "
"\"<EMAIL>\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Registrar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Log RTC events"
msgstr "Registrar eventos de RTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "Registrar una nota..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "Registrar una actividad"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/composer_patch.js:0
#, python-format
msgid "Log an internal note…"
msgstr "Registrar nota interna..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Log note"
msgstr "Registrar una nota"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Log step:"
msgstr "Registrar paso:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#, python-format
msgid "Logged in as %s"
msgstr "Iniciada sesión como %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "Información de inicio de sesión"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "Baja"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Lower Hand"
msgstr "Bajar la mano"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr "Tipo de MIME"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "Correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "Actividad de correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Tipo de actividad del correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "Lista negra de correos"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "Mixin de la lista negra de correos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "Formulario de canal de correo"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "Mixin del redactor de correo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Mail Failures"
msgstr "Fallos de correo"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr "Puerta de enlace de correo permitida"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
#, python-format
msgid "Mail Layout"
msgstr "Diseño del correo"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr "Gestión de los principales archivos adjuntos del correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr "Int ID del mensaje de correo"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr "Sesión de comunicación en tiempo real por correo"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mixin de renderización de correo"

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "Servidor de correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "Plantilla de correo electrónico"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "Editor de plantillas de correo"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr "Restablecimiento de la plantilla de correo"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
#, python-format
msgid "Mail Templates have been reset"
msgstr "Se han restablecido las plantillas de correo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Hilo de correos"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Valor de seguimiento de correo"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr ""
"El compositor de correo en modo comentario debe ejecutarse en al menos un "
"registro. No se han encontrado registros (modelo %(model_name)s)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"Se ha creado un correo electrónico para notificar a la gente de un mensaje "
"existente"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Mail template model of %(action_name)s does not match action model."
msgstr ""
"El modelo de plantilla de correo de %(action_name)s no coincide con el "
"modelo de acción."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr "Plantilla de correo que utiliza este servidor de correo"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr "Correo: gerente de la cola de correo electrónico"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "Correo: servicio de Fetchmail"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr "Correo: enviar notificaciones push en web"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Bandeja no disponible - %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Mailboxes"
msgstr "Buzones de correo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr ""
"El envío por correo o la publicación con una fuente no debe llamarse con un "
"%(source_type)s vacío"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "Correos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "Administrar mensajes"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr ""
"Gestione respuestas como correos electrónicos entrantes nuevos en lugar de "
"respuestas al mismo hilo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/notification_item.xml:0
#, python-format
msgid "Mark As Read"
msgstr "Marcar como leído"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Mark Done"
msgstr "Marcar como hecho"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Mark all read"
msgstr "Marcar todo como leido"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree_without_record_access
msgid "Mark as Done"
msgstr "Marcar como hecho"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Read"
msgstr "Marcar como leido"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Todo"
msgstr "Marcar como pendiente"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Unread"
msgstr "Marcar como no leído"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Mark as done"
msgstr "Marcar como hecho"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
#, python-format
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr ""
"Dispositivos multimedia no disponibles. Es posible que SSL no esté "
"configurado correctamente."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "Medio"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Reunión"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr "Número de miembros"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
#, python-format
msgid "Member List"
msgstr "Lista de miembros "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "Miembros"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Los miembros de estos grupos se añadirán automáticamente como seguidores. "
"Tenga en cuenta que podrán gestionar su suscripción manualmente si es "
"necesario."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
#, python-format
msgid "Mentions"
msgstr "Menciones"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
#, python-format
msgid "Mentions Only"
msgstr "Solo menciones"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Asistente de fusión de contactos"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Fusionado con los siguientes contactos:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "Mensaje"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message #%(thread name)s…"
msgstr "Mensaje a #%(thread name)s…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message %(thread name)s…"
msgstr "Mensaje a %(thread name)s…"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "ID del mensaje"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Notificaciones de mensajes"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "Reacción de mensaje"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "Reacciones de mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Nombre de registro del mensaje"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr "Traducción del mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr "Clave API de traducción del mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Tipo de mensaje"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr ""
"Descripción del mensaje: o bien el asunto, o el comienzo del cuerpo del "
"mensaje"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr "Mensaje publicado en “%s”"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Destinatarios del mensaje (correos electrónicos)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr ""
"Referencias del mensaje, tales como identificadores de mensajes anteriores"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr "El mensaje debe ser una instancia de EmailMessage válida"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"El subtipo de mensaje da un tipo más preciso en el mensaje, especialmente "
"para los sistema de notificación. Por ejemplo, puede ser una notificación "
"relativa a un nuevo registro (Nuevo), o a un cambio de etapa en un proceso "
"(Cambio de etapa). Los subtipos de mensaje permiten afinar las "
"notificaciones que los usuarios quieren recibir en su muro."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Subtipos de mensaje"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Subtipos de mensaje seguidos. Serán los subtipos que serán llevados al muro "
"del usuario."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Tipo de mensaje: correo electrónico para mensajes de correo electrónico, "
"notificación para mensajes del sistema, comentario para otros tipos de "
"mensaje como respuestas de usuarios."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Identificador único del mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Id del mensaje"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "Mensajes"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Búsqueda de mensajes"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr "Los mensajes marcados como leídos aparecerán en el historial."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Los mensajes con subtipos internos solo serán visibles para los empleados, "
"es decir, los miembros del grupo base_user"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr "No se pueden modificar los mensajes con valores de seguimiento"

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr "Metadatos para los archivos adjuntos de voz"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "Correo electrónico faltante"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "Dirección de correo electrónico faltante"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr "Falta en la dirección"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr ""
"Mixin para calcular el tiempo que ha pasado un registro en cada valor que "
"puede tomar un campo many2one"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "Modelo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "El modelo ha cambiado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Modelo del recurso seguido"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Modelo al que aplica el subtipo. Si no está establecido, este subtipo aplica"
" a todos los modelos."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Modelos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"La modificación del modelo puede tener un impacto en las actividades "
"existentes que utilizan este tipo de actividad, tenga cuidado."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Desinstalar módulo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Meses"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "More"
msgstr "Más"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Mute"
msgstr "Silenciar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#, python-format
msgid "Mute Channel"
msgstr "Silenciar canal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
msgid "Mute notifications until"
msgstr "Silenciar las notificaciones hasta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "Mis plantillas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "Nombre"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Requiere acción"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "New Channel"
msgstr "Nuevo canal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "New Message"
msgstr "Nuevo mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Nuevo valor de carácter"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Nuevo valor de fecha y hora"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Nuevo valor de coma flotante"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Nuevo valor de entero"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Nuevo valor de texto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
#, python-format
msgid "New message"
msgstr "Nuevo mensaje"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "New messages"
msgstr "Nuevo mensaje"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "New messages appear here."
msgstr "Los mensajes nuevos se muestran aquí."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "Nuevos valores"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Siguientes actividades"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "Siguiente actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Siguientes actividades disponibles"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "No"
msgstr "No"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "Sin error"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "No Followers"
msgstr "Sin seguidores"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "No IM status available"
msgstr "No está disponible el estado de la mensajería instantánea"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "Sin registro"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr "Sin actividades."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "No channel found"
msgstr "No se encontraron canales"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr "No se seleccionó ninguna conversación."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "Ninguna conversación aún..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "No history messages"
msgstr "Sin mensajes de historial"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr "Nose ha encontrado ningún message_id en contexto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
#, python-format
msgid "No messages found"
msgstr "No se ha encontrado ningún mensaje"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "No recipient"
msgstr "Sin destinatarios"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr "No se encontró destinatario."

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"No se ha recibido respuesta. Compruebe la información del servidor.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr ""
"No se especifica ningún responsable para %(activity_type_name)s: "
"%(activity_summary)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "No results found"
msgstr "No se encontraron resultados"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "No starred messages"
msgstr "No hay mensajes destacados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "No hay un hilo para las respuestas"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "No user found"
msgstr "No se ha encontrado ningún usuario"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr "No se ha encontrado ningún usuario que no sea miembro de este canal."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "No users found"
msgstr "No se encontraron usuarios"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
#, python-format
msgid "Non existing record or wrong token."
msgstr "Registro no existente o token incorrecto."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#, python-format
msgid "None"
msgstr "Ninguno"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "Correo electrónico normalizado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "No confirmado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr "No probado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "Nota"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#, python-format
msgid "Nothing"
msgstr "Nada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Notificación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "Correo electrónico de notificación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/notification_item.xml:0
#, python-format
msgid "Notification Item Image"
msgstr "Imagen del elemento de notificación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr "Parámetro de notificación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Notification Settings"
msgstr "Ajustes de notificación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Tipo de notificación"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""
"Las notificaciones deben recibir archivos adjuntos como una lista de listas "
"o tuplas (recibió %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr ""
"Las notificaciones deben recibir archivos adjuntos como una lista de IDs "
"(recibió %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr ""
"Las notificaciones deben recibir contactos asignados como una lista de IDs "
"(recibió %(pids)s)"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr ""
"Notificación: eliminar notificaciones con más de 6 meses de antigüedad"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Send scheduled message notifications"
msgstr "Notificación: enviar notificaciones de mensajes programados"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "Notificaciones"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Notifications allowed"
msgstr "Notificaciones permitidas"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Notifications blocked"
msgstr "Notificaciones bloqueadas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"Número de días/semanas/meses antes de realizar la acción. Permite planear la"
" fecha límite de la acción."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Odoo will not send notifications on this device."
msgstr "Odoo no enviará notificaciones a este dispositivo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Odoo will send notifications on this device!"
msgstr "¡Odoo enviará notificaciones a este dispositivo!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr "Apagado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Offline"
msgstr "Desconectado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Offline -"
msgstr "Desconectado -"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Valor antiguo de carácter"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Valor antiguo de fecha y hora"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Valor antiguo de coma flotante"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Valor antiguo del entero"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Valor antiguo de texto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "Valores antiguos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Online"
msgstr "En línea"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Online -"
msgstr "En línea -"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr "Solo los administradores pueden exportar mensajes de correo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "Solo los modelos personalizados pueden ser modificados."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr "Solo los usuarios internos pueden recibir notificaciones en Odoo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr ""
"Solo los mensajes de tipo comentario pueden tener su contenido actualizado"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr ""
"Solo se puede actualizar el contenido de los mensajes de tipo comentario en "
"el modelo 'discuss.channel'"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Only users belonging to the \"%(group_name)s\" group can modify dynamic "
"templates."
msgstr ""
"Solo los usuarios que pertenecen al grupo \"%(group_name)s\" pueden "
"modificar las plantillas dinámicas."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
#, python-format
msgid "Open"
msgstr "Abierto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
#, python-format
msgid "Open Actions Menu"
msgstr "Abrir el menú de acciones"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "Abrir documento"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Open Form View"
msgstr "Abrir vista de formulario"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Open Link"
msgstr "Enlace abierto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr "Abrir el propietario"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "Open card"
msgstr "Abrir tarjeta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
#, python-format
msgid "Open in Discuss"
msgstr "Abrir en Conversaciones"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operación no admitida"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "Exclusión voluntaria"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID opcional de un hilo (registro) al que se adjuntarán todos los mensajes "
"entrantes, incluso si no fueron respuestas del mismo. Si se establece, se "
"deshabilitará completamente la creación de nuevos registros."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr ""
"ID de mail_mail opcional. Se utiliza principalmente para optimizar "
"búsquedas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Servidor de salida preferido (opcional). Si no está establecido, se usará el"
" de mayor prioridad."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma de traducción opcional (código ISO) a seleccionar para el envío de "
"correos electrónicos. Si no se selecciona esta opción, se utilizará la "
"versión en inglés. Por lo general, se usa una expresión de marcador de "
"posición para indicar el idioma adecuado, por ejemplo, {{ "
"object.partner_id.lang }}."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"Conversación original: las respuestas se dirigen al hilo de conversación del documento original.\n"
"Otra dirección de correo electrónico: las respuestas se dirigen a la dirección de correo electrónico mencionada en el message-id de seguimiento en lugar de al hilo de discusión del documento original.\n"
"Esto tiene un impacto en el message-id generado."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#, python-format
msgid "Original message was deleted"
msgstr "Se eliminó el mensaje original"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
#, python-format
msgid "Other activities"
msgstr "Otras actividades"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Saliente"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr "Correo electrónico saliente"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "Servidores de correo de salida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Servidor de correo de salida"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Correos electrónicos salientes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Servidor de correo de salida"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "De retraso"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Sobreescribir correo electrónico del autor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "Servidor POP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "Servidores POP/IMAP"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Packets received:"
msgstr "Paquetes recibidos:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Packets sent:"
msgstr "Paquetes enviados:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Padre"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Mensaje padre"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "Modelo padre"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID del hilo del registro padre"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modelo padre que contiene el alias. El modelo que contiene la referencia del"
" alias no es necesariamente el modelo dado por alias_model_id (example: "
"project (parent_model) and task (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Subtipo padre, utilizado para la suscripción automática. Este campo no esta "
"correctamente nombrado. Por ejemplo en un proyecto, el parent_id del subtipo"
" de proyecto se refiere a los subtipos de tareas relacionadas."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "Contacto"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "Perfil del contacto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr "Contacto de sólo lectura "

#. module: mail
#: model:ir.model,name:mail.model_mail_partner_device
msgid "Partner Web Push Device"
msgstr "Dispositivo push del contacto web"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "Contacto con información adicional para el reenvío del correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "Contactos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Contactos con acción requerida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "Contraseña"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr "Pegar la clave API"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Pause"
msgstr "Detener"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__payload
msgid "Payload"
msgstr "Carga efectiva"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr "Eliminar permanentemente esta plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "Teléfono"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "Llamada telefónica"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_patch.js:0
#, python-format
msgid "Pin"
msgstr "Fijar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Pin It"
msgstr "Fíjelo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr "Fijado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
#, python-format
msgid "Pinned Messages"
msgstr "Mensajes fijados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "Plan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr "Plan disponible"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date_deadline
msgid "Plan Date"
msgstr "Fecha del plan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "Nombre del plan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__assignation_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_assignation_summary
msgid "Plan Summary"
msgstr "Resumen del plan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr "Resumen del plan"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__planned
#, python-format
msgid "Planned"
msgstr "Planificado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Planned Activities"
msgstr "Actividades planificadas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Planificado en"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "Planificación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Play"
msgstr "Iniciar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr "Complete la información del cliente"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Póngase en contacto con nosotros a través de"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr "Espere mientras se sube el archivo."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Política de gestión de las notificaciones:\n"
"- Gestionar por correo electrónico: las notificaciones son enviadas a su dirección de correo.\n"
"- Gestionar en Odoo: las notificaciones aparecen en su bandeja de entrada de Odoo."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Política para enviar un mensaje en el documento usando la pasarela de correo.\n"
"- Todos: cualquiera puede enviar\n"
"- Contactos: sólo contactos autenticados\n"
"- Seguidores: sólo seguidores del documento relacionado o miembros de los siguientes canales\n"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr "Puerto"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr "Acceso al portal otorgado"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr "Acceso al portal revocado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "Publicar en un documento"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""
"La publicación de un mensaje debe hacerse en un documento comercial. Utilice"
" message_notify para enviar una notificación a un usuario."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""
"La publicación de un mensaje debe recibir archivos adjuntos como una lista o"
" tuplas (recibió %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr ""
"La publicación de un mensaje debe recibir registros de archivos adjuntos "
"como una lista de IDs (recibió %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr ""
"La publicación de un mensaje debe recibir contactos como una lista de IDs "
"(recibió %(pids)s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "Con tecnología de"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Actividad precedente"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "Dirección de respuesta preferida"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "Press Enter to start"
msgstr "Presionar Enter para iniciar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr ""
"Presione una tecla para registrarla como el acceso rápido de pulsar para "
"hablar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Vista previa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "Vista previa de"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Tipo de actividad anterior"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "Privacidad"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"Procese cada correo entrante como parte de una conversación correspondiente "
"a este tipo de documento. Esto creará nuevos documentos para nuevas "
"conversaciones, o adjuntará correos electrónicos de seguimiento a las "
"conversaciones existentes (documentos)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
#, python-format
msgid "Processing"
msgstr "Procesando"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Public Channel"
msgstr "Canal público"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid "Public Channels"
msgstr "Canales públicos"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Contrato de garantía del editor"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr "Editor: actualizar la notificación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "Acceso rápido de la función pulsar para hablar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr "Tecla de la función pulsar para hablar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Quick search..."
msgstr "Búsqueda rápida..."

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "Sesión de comunicación en tiempo real"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "RTC Session ID:"
msgstr "ID de la sesión de comunicación en tiempo real:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr "Sesiones de comunicación en tiempo real"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "Sesiones de comunicación de tiempo real"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Raise Hand"
msgstr "Levantar la mano"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr ""
"Rangos entre 0.0 y 1.0, la escala depende de la implementación del "
"navegador."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "Reacción del invitado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "Reacción del partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr "Reacciones"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "Fecha de lectura"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#, python-format
msgid "Ready"
msgstr "Listo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Listo para enviar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "Razón"

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr "Recibir notificaciones en Odoo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Recibido"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
#, python-format
msgid "Recent"
msgstr "Reciente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Destinatario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr "Nombre del destinatario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "Destinatarios"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr "Actividades recomendadas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Tipo de actividad recomendado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Registro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr "Nombre del registro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID del hilo de registro"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "Registros:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Referencias"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Refuse"
msgstr "Rechazar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "Saludos, "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Register new key"
msgstr "Registrar nueva clave"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Reject"
msgstr "Rechazar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "Compañía relacionada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "ID del documento relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr "IDs de los documentos relacionados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Modelo de documento relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Nombre del modelo del documento relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "Plantilla de correo relacionada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Mensaje relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Contacto relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Campo de relación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
#, python-format
msgid "Remove"
msgstr "Eliminar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr "Eliminar la acción contextual"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr "Eliminar seguidores"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "Asistente para eliminar correo electrónico de la lista negra"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Elimine la acción contextual para utilizar esta plantilla en documentos "
"relacionados"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "Remove this follower"
msgstr "Eliminar este seguidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr "Información de campo eliminada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Rename"
msgstr "Renombrar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "Modelo de visualización"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr ""
"No es posible visualizar %(field_name)s, no hay contrapartida en la "
"plantilla."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr ""
"No es posible visualizar %(field_name)s, no se definió en la plantilla."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Repeat"
msgstr "Repetir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "Respuestas"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Responder"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "Responder a"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Dirección de respuesta de los correos. Estableciendo este campo, se saltará "
"la creación automática de hilos."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "Responder a"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr "Dirección de correo electrónico de respuesta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Replying to"
msgstr "Respondiendo a"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Report"
msgstr "Informe"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "Contacto solicitante"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "Reenviar"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr "Reenviar correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "Asistente de reenvio"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "Restablecer confirmación"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr "Restablecer plantilla de correo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr "Restablecer plantilla"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr "Reestablecer su contraseña"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "Responsable"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "Restringir la creación de plantillas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr ""
"Restrinja la edición de plantillas de correo y el uso de marcadores de "
"posición QWEB"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr "Archivos adjuntos restringidos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr "Resultado de la detección del idioma según su contenido."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Volver a intentar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Revert"
msgstr "Revertir"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr "Ver todas las plantillas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "Contenido con texto enriquecido"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Mensaje de texto enriquecido/HTML"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr "Sesión de llamadas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "Sesión de comunicación en tiempo real"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr "URL del servidor SFU"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr "Clave del servidor SFU"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr "Servidor SFU"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "Servidor SMTP"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Comercial"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "Save"
msgstr "Guardar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save Template"
msgstr "Guardar plantilla"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Guardar como plantilla nueva"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Save editing"
msgstr "Guardar edición"

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr "Marcar un GIF de Tenor API como favorito "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Programar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr "Programar y marcar como hecho"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Schedule Activity"
msgstr "Programar actividad"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Schedule Activity On Selected Records"
msgstr "Programar actividad en registros seleccionados"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr "Programe actividades que le ayuden a hacer las cosas."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "Schedule activity"
msgstr "Planificar actividad"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "Programar una actividad"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "Programar una actividad"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule an activity on selected records"
msgstr "Programar una actividad en los registros seleccionados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Scheduled Message"
msgstr "Mensaje programado"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr "Mensajes programados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Fecha de envío programada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "Guion"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search"
msgstr "Buscar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "Buscar alias"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "Buscar grupos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "Buscar en los servidores de correo de entrada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Search Messages"
msgstr "Buscar mensajes"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "Buscar sesión de comunicación en tiempo real"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search button"
msgstr "Botón de búsqueda"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Search for a GIF"
msgstr "Buscar un GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "Search for a channel..."
msgstr "Buscar un canal..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "Search for a user..."
msgstr "Buscar un usuario..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search in progress"
msgstr "Búsqueda en proceso"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
#, python-format
msgid "Search messages"
msgstr "Buscar mensajes"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Search..."
msgstr "Buscar…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr "Buscar: %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Email Changed"
msgstr "Actualización de seguridad: correo electrónico cambiado"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Login Changed"
msgstr "Actualización de seguridad: nombre de usuario cambiado"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Password Changed"
msgstr "Actualización de seguridad: contraseña cambiada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr "Consultar detalles del error"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "See all pinned messages."
msgstr "Vea todos los mensajes fijados."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr "Seleccionar un idioma"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Select a user..."
msgstr "Seleccionar un usuario..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr "Seleccionar el filtro de contenido utilizado para filtrar GIFs"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Selected users:"
msgstr "Usuarios seleccionados:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Enviar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr "Enviar y cerrar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr "Enviar correo electrónico como"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "Enviar correo electrónico (%s)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Send Notification"
msgstr "Enviar notificación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Enviar ahora"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/composer_patch.js:0
#, python-format
msgid "Send a message to followers…"
msgstr "Enviar mensaje a los seguidores..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr "Envíe y reciba correos electrónicos desde su cuenta de Gmail."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr "Envíe y reciba correos electrónicos desde su cuenta de Outlook."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "Enviar correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr "Enviar correo o notificaciones directamente"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Send message"
msgstr "Enviar mensaje"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "Dirección del remitente"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Dirección del remitente (se pueden usar expresiones de campo aquí). Si no se"
" establece, el valor por defecto será el alias de correo electrónico del "
"autor si está configurado, o la dirección de correo electrónico."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr "Fallos de envío "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "Enviado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "Servidor e inicio de sesión"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Acción de servidor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "Información del servidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "Nombre del servidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "Prioridad del servidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Tipo de servidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr "Información sobre el tipo de servidor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
#, python-format
msgid "Server error"
msgstr "Error de servidor"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"El servidor respondió con la siguiente excepción:\n"
" %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "Tipo de servidor IMAP."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "Tipo de servidor POP."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "Establezca activo en False para ocultar el canal sin eliminarlo."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "Ajustes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr "Uuid del canal SFU"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr "URL del servidor SFU"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Share Screen"
msgstr "Compartir pantalla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr "Descripción corta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Codigos cortos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Acceso rápido"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid ""
"Shortcut that will automatically be substituted with longer content in your "
"messages. Type ':' followed by the name of your shortcut (e.g. :hello) to "
"use in your messages."
msgstr ""
"Acceso directo que se sustituirá automáticamente por un contenido más largo "
"en sus mensajes. Escriba ':' seguido del nombre de su acceso rápido (por "
"ejemplo, :hola) para utilizarlo en sus mensajes."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr ""
"Código abreviado del idioma utilizado como destino de la solicitud de "
"traducción."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Show Attachments"
msgstr "Mostrar archivos adjuntos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Show Call Settings"
msgstr "Mostrar los ajustes de llamada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "Show Followers"
msgstr "Mostrar seguidores"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Show Member List"
msgstr "Mostrar lista de miembros"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#, python-format
msgid "Show a helper message"
msgstr "Mostrar un mensaje de ayuda"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#, python-format
msgid "Show activities"
msgstr "Mostrar actividades"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Show all recipients"
msgstr "Mostrar todos los destinatarios"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr "Mostrar menos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr "Mostrar más"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
#, python-format
msgid "Show sidebar"
msgstr "Mostrar barra lateral"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Show video participants only"
msgstr "Mostrar solo a los participantes del vídeo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Showing"
msgstr "Mostrando"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Acción de barra lateral"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Acción de la barra lateral para hacer disponible esta plantilla en los "
"registros del modelo de documento relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr "Nombre del sitio"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "So uhh... maybe go favorite some GIFs?"
msgstr "¿Por qué no agrega algunos gifs a sus favoritos?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "Origen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr "Idioma de origen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "Usuario específico"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Especificar un modelo si la actividad debería ser específica para un modelo "
"y no estar disponible cuando se gestionan actividades para otros modelos."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Destacado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr "Mensaje destacado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Start a Call"
msgstr "Iniciar llamada"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Start a Conversation"
msgstr "Iniciar una conversación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "Start a conversation"
msgstr "Iniciar una conversación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/web/discuss_sidebar_start_meeting.xml:0
#, python-format
msgid "Start a meeting"
msgstr "Iniciar reunión"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "Estado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Estado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"De retraso: la fecha límite ya ha pasado\n"
"Hoy: la fecha límite es hoy\n"
"Planificada: actividades futuras."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr "Tiempo del estado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
#, python-format
msgid "Status with time"
msgstr "Estado con tiempo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr "Número de pasos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Stop Adding Users"
msgstr "Dejar de añadir usuarios"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
#, python-format
msgid "Stop Recording"
msgstr "Detener grabación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Stop Sharing Screen"
msgstr "Dejar de compartir pantalla"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Stop camera"
msgstr "Detener cámara"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr "Dejar de responder"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr ""
"Guarde los correos electrónicos y las respuestas en el chatter de cada "
"registro"

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr "Almacenar los datos de la vista previa del enlace"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"Cadena con formato que representa una tecla con modificadores que siguen "
"este patrón: shift.ctrl.alt.key, por ejemplo: truthy.1.True.b"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr "Cadena recibida de la solicitud de traducción."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "Asunto"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "Asunto (se pueden utilizar aquí expresiones de campos)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Subject:"
msgstr "Asunto:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "Suscribir a los destinatarios"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Sustitución"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Subtipo"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Subtipo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "Sugerir"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "Sugerir siguiente actividad"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr ""
"Sugiera estas actividades una vez que la actual esté marcada como hecha."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "Resumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "Resumen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr "Soporte para la autenticación de Gmail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr "Soporte para la autenticación de Outlook"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "Parámetro del sistema"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "Notificación del sistema"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr "Idioma de destino"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "Modelo dirigido"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "NIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Technical Settings"
msgstr "Ajustes técnicos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""
"Campo técnico para llevar el seguimiento del modelo desde el inicio de su "
"edición con el fin de favorecer el comportamiento relacionado con la "
"experiencia del usuario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "Plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr "Categoría de la plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr "Nombre del archivo de la plantilla"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "Vista previa de la plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "Idioma de vista previa de la plantilla"

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr "Mixin de restablecimiento de plantilla"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Template creation from composer requires a valid model."
msgstr "La creación de plantillas desde el editor requiere un modelo válido."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template description"
msgstr "Descripción de la plantilla"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering should be called only using on a list of IDs; received "
"%(res_ids)r instead."
msgstr ""
"La visualización de plantillas se debe llamar solo usando una lista de IDs; "
"en cambio, recibió %(res_ids)r."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr ""
"La visualización de plantillas sólo admite inline_template, qweb o qweb_view"
" (vista o bruto); en cambio, recibió %(engine)s."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Plantillas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr "Clave API de Tenor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr "Clave de API de Tenor GIF"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr "Filtro de contenido de Tenor GIF"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr "Límites de Tenor GIF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr "Límite de Tenor GIF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr "Filtro de contenido de Tenor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "Probar y confirmar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr "Registro de la prueba:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr "Contenidos del texto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "El equipo de"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "El valor 'Fecha de vencimiento' no puede ser negativo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
#, python-format
msgid "The Fullscreen mode was denied by the browser"
msgstr "El navegador rechazó el modo de pantalla completa"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""
"El número de identificación fiscal. Los valores establecidos aquí se "
"validarán según el formato del país. Puede utilizar \"/\" para indicar que "
"el contacto no está sujeto a impuestos."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The activity cannot be launched:"
msgstr "No se puede iniciar la actividad:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""
"El tipo de actividad \"%(activity_type_name)s\" no es compatible con el plan"
" \"%(plan_name)s\" porque está limitado al modelo "
"\"%(activity_type_model)s\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr ""
"El archivo adjunto %s no existe o usted no tiene los permisos para acceder a"
" él."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr "El archivo adjunto %s no existe."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.js:0
#, python-format
msgid "The avatar has been updated!"
msgstr "¡El avatar ha sido actualizado!"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "El UUID del canal debe ser único"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr "El tipo de canal no puede estar vacío"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr "El usuario actual puede editar la plantilla."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "The duration of voice messages is limited to 1 minute."
msgstr "La duración de los mensajes de voz está limitada a 1 minuto."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "El correo electrónico enviado a"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_partner_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr "El punto de conexión debe ser único"

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
#, python-format
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""
"No se han podido restablecer las siguientes plantillas de correo electrónico porque no se han podido encontrar los archivos de origen relacionados:\n"
"- %s"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "El usuario interno a cargo de este contacto."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr "El último mensaje recibido en este alias ha provocado un error."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr "El siguiente mensaje no ha podido ser aceptado por la dirección"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""
"La dirección %(alias_display_name)s no pudo aceptar el siguiente mensaje.\n"
"                 Solo %(contact_description)s puede ponerse en contacto.<br /><br />\n"
"                 Asegúrese de que está utilizando la dirección correcta o póngase en contacto con nosotros en %(default_email)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""
"El siguiente mensaje no ha podido ser aceptado por la dirección %(alias_display_name)s.\n"
"Inténtelo de nuevo más tarde o póngase en contacto con %(company_name)s."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"El modelo (Tipo de documento de Odoo) al que corresponde este alias. "
"Cualquier correo entrante que no sea respuesta a un registro existente, "
"causará la creación de un nuevo registro de este modelo (por ejemplo, una "
"tarea de proyecto)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nombre de este alias de correo electrónico. Por ejemplo, \"trabajos\",  "
"si lo que quiere es obtener los correos para <<EMAIL>>."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr "No se puede poner en marcha el plan \"%(plan_name)s\":"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The plan \"%(plan_name)s\" has been started"
msgstr "Se ha puesto en marcha el plan \"%(plan_name)s\":"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr ""
"El administrador de colas enviará el correo electrónico después de la fecha"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The records must belong to the same company."
msgstr "Los registros deben pertenecer a la misma empresa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"No se ha podido completar la operación por restricciones de seguridad. Contacte por favor con su administrador de sistema.\n"
"\n"
"(Tipo de documento: %s, Operación: %s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "The server \"%s\" cannot be used because it is archived."
msgstr "El servidor \"%s\" no se puede usar porque está archivado."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr "Se han aplicado correctamente las preferencias de su suscripción."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr "La plantilla pertenece a este usuario"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr ""
"El comienzo del cuerpo de texto utilizado como vista previa del correo "
"electrónico."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr "No hay mensajes en esta conversación."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr "Solo puede haber una sesión de RTC por miembro de canal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Este"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "This action can only be done on a mail thread models"
msgstr "Esta acción sólo puede realizarse en un modelo de hilo de correo"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "This action cannot be done on transient models."
msgstr "Esta acción no puede realizarse en modelos transitorios."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "This action will send an email."
msgstr "Esta acción enviará un correo electrónico."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "This channel doesn't have any attachments."
msgstr "Este canal no tiene archivos adjuntos."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#, python-format
msgid "This channel doesn't have any pinned messages."
msgstr "Este canal no tiene ningún mensaje fijado."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "This conversation doesn't have any attachments."
msgstr "Esta conversación no tiene archivos adjuntos."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#, python-format
msgid "This conversation doesn't have any pinned messages."
msgstr "Esta conversación no tiene ningún mensaje fijado."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Este correo electrónico está en la lista negra para correos masivos. Haga "
"clic para eliminarlo de ella."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "Este campo no distingue entre mayúsculas y minúsculas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr ""
"Este campo se utiliza para la descripción interna del uso de la plantilla."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Este campo se utiliza para buscar en la dirección de correo electrónico, ya "
"que el campo de correo electrónico principal puede contener más de una "
"dirección de correo electrónico."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
#, python-format
msgid "This layout seems to no longer exist."
msgstr "Parece que este diseño ya no existe."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"Esta opción borra cualquier rastro del envío del correo, incluido el menú "
"Técnico en los Ajustes, para preservar el espacio de almacenamiento de su "
"base de datos Odoo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
#, python-format
msgid "This record has an exception activity."
msgstr "Este registro tiene una actividad de excepción."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr ""
"Esos valores no se admiten como opciones al visualizar: %(param_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr "Esos valores no se admiten al publicar o notificar: %(param_names)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Hilo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Thread Image"
msgstr "Imagen de hilo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr "Hilos activados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "Zona horaria"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "Título"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr "A"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "A (correos electrónicos)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "A (empresas)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
#, python-format
msgid "To :"
msgstr "A:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "To peer:"
msgstr "A colega:"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr "Actividades pendientes"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "To:"
msgstr "A:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__today
#, python-format
msgid "Today"
msgstr "Hoy"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Today:"
msgstr "Hoy:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Tomorrow"
msgstr "Mañana"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Tomorrow:"
msgstr "Mañana:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Temas discutidos en este grupo..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr "Rastrear destinatarios"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Los valores seguidos están almacenados en un modelo separado. Este campo "
"permite reconstruir el seguimiento y generar estadísticas en el modelo."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "Seguimiento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Valor de seguimiento"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Valores de seguimiento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Valores de seguimiento"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Translate"
msgstr "Traducir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr "Texto de traducción"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Translation Failure"
msgstr "Error de traducción"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "Activador"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "Activar la siguiente actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr "Vuelva a intentarlo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Turn camera on"
msgstr "Encender cámara"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Token de autentificación de cuenta de Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "SID de cuenta de Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Tipo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "Tipo de retraso"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Tipo de acción de servidor. Los siguientes valores están disponibles:\n"
"- 'Actualizar un registro': actualizar los valores de un registro\n"
"- 'Crear actividad': crear una actividad (conversaciones)\n"
"- 'Enviar correo electrónico': publicar un mensaje, una nota o enviar un correo electrónico (Conversaciones)\n"
"- 'Enviar SMS': enviar SMS y registrarlos en los documentos (SMS)- 'Añadir o eliminar seguidores': añadir o eliminar seguidores de un registro (Conversaciones)\n"
"- 'Crear registro': crear un nuevo registro con nuevos valores\n"
"- 'Ejecutar código': un bloque de código Python que se ejecutará\n"
"- 'Enviar notificación webhook': enviar una solicitud POST a un sistema externo, también conocido como Webhook\n"
"- 'Ejecutar acciones existentes': definir una acción que activa otras acciones de servidor\n"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción en el registro."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Type the name of a person"
msgstr "Escriba el nombre de la persona"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "Imposible conectar con el servidor SMTP"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"No se puede publicar el mensaje, configure la dirección de correo "
"electrónico del remitente."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to send message, please configure the sender's email address."
msgstr ""
"No se puede enviar el mensaje, configure la dirección de correo electrónico "
"del remitente."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Unassign"
msgstr "Eliminar la asignación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Unassign from me"
msgstr "Eliminar mi asignación"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "Eliminar de la lista negra"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
#, python-format
msgid "Unblock Reason: %(reason)s"
msgstr "Razón de desbloqueo: %(reason)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Undeafen"
msgstr "Desactivar ensordecimiento"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/chatter.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#, python-format
msgid "Unfollow"
msgstr "Dejar de seguir"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "Unidad de retraso"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
#, python-format
msgid "Unknown"
msgstr "Desconocido"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "Error desconocido"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Unmute"
msgstr "Desactivar silencio"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#, python-format
msgid "Unmute Channel"
msgstr "Desactivar silencio del canal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_patch.js:0
#, python-format
msgid "Unpin"
msgstr "Desfijar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr "Desfijar conversación"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Unpin Message"
msgstr "Desfijar mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contador de mensajes sin leer"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "Mensajes sin leer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr "Archivos adjuntos sin restricción"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Unstar all"
msgstr "Quitar todos de favoritos"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "Tipo de informe desconocido %s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.js:0
#, python-format
msgid "Until "
msgstr "Hasta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "Until I turn it back on"
msgstr "Hasta que se vuelva a activar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr "Actualice diseño del correo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "Upload Avatar"
msgstr "Subir avatar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "Subir documento"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Upload File"
msgstr "Subir archivo "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Upload file"
msgstr "Subir archivo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Upload:"
msgstr "Subir:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Uploaded"
msgstr "Subido"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Uploading"
msgstr "Subiendo"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr ""
"Utilice 'Usuario específico' para asignar siempre al mismo usuario en la "
"siguiente actividad. Utilice 'Usuario dinámico' para especificar el nombre "
"del campo del usuario a elegir en el registro."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr "Utilizar servidores de correo electrónico personalizados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "Usar los servicios ICE de Twilio"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr "Usar un servidor de Gmail"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "Use a local script to fetch your emails and create new records."
msgstr ""
"Utilice un script local para obtener sus correos electrónicos y crear nuevos"
" registros."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr "Usar un servidor de Outlook"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr "Utilice diferentes dominios para sus alias de correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr "Usar en lote"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Use message_notify to send a notification to an user."
msgstr "Use message_notify para enviar una notificación a un usuario."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Usar plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "Usar la función pulsar para hablar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "Utilizado en"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr "Se utiliza como contexto para evaluar el dominio del compositor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Utilizado para categorizar el generador de mensajes\n"
"'email': generado por un correo electrónico entrante, por ejemplo, mailgateway\n"
"'comment': generados por la entrada del usuario, por ejemplo, a través de conversaciones o del compositor\n"
"'email_outgoing': generado por un envío de correo\n"
"'notification': generados por el sistema, por ejemplo, mensajes de seguimiento\n"
"'auto_comment': generado por un mecanismo de notificación automatizado, por ejemplo un acuse de recibo\n"
"'user_notification': generado para un destinatario específico"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr ""
"Utilizado para mostrar la moneda cuando se hace un seguimiento de los "
"valores monetarios"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Usado para ordenar los subtipos."

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "Usuario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr "Campo de usuario"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "Conexión del usuario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "Ajustes de usuario"

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "Ajustes de usuario"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "Ajustes de volumen del usuario"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "Notificación específica del usuario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "Tipo de usuario"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is a bot"
msgstr "El usuario es un bot"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is idle"
msgstr "Usuario está inactivo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is offline"
msgstr "Usuario está desconectado"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is online"
msgstr "Usuario está conectado"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr "El usuario no debió de duplicar el GIF favorito"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "Usuario:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "Nombre de usuario"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr "Usuarios en este canal: %(members)s %(dots)s y usted."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"Los usuarios aún podrán visualizar las plantillas.\n"
"Sin embargo, solo los editores de plantillas de correo podrán crear nuevas plantillas dinámicas o modificar las existentes."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"El uso de un servidor propio de correo electrónico es necesario para "
"enviar/recibir correos en las versiones Community y Enterprise. Los usuarios"
" de la versión Online si tienen disponible un servidor de correo listo para "
"usar (@micompania.odoo.com)."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "Válido"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"No se puede validar el valor %(allowed_domains)s para `mail.catchall.domain.allowed`.\n"
"Debe ser una lista de dominios separados por comas, p. ej. ejemplo.com,ejemplo.org."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Video Settings"
msgstr "Ajustes de vídeo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Video player:"
msgstr "Reproductor de vídeo:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#, python-format
msgid "View"
msgstr "Ver"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "Ver %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "View Reactions"
msgstr "Ver reacciones"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Tipo de vista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "View or join channels"
msgstr "Ver o unirse a canales"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "Voz"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
#, python-format
msgid "Voice Message"
msgstr "Mensaje de voz"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
#, python-format
msgid "Voice Settings"
msgstr "Ajustes de voz"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Voice detection threshold"
msgstr "Alcance de la detección de voz"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "Voice recording stopped"
msgstr "Grabación de voz detenida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "Volumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "Volumen por contacto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "Volúmenes de otros contactos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr ""
"¿Quiere alegrar sus conversaciones con GIFs? Active la función en los "
"ajustes."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
#, python-format
msgid "Warning"
msgstr "Advertencia"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""
"No se pudo crear el alias %(alias_name)s porque el dominio "
"%(alias_domain_name)s pertenece a la empresa %(alias_company_names)s "
"mientras que el documento propietario pertenece a la empresa "
"%(company_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""
"No se pudo crear el alias %(alias_name)s porque el dominio "
"%(alias_domain_name)s pertenece a la empresa %(alias_company_names)s "
"mientras que el documento de destino pertenece a la empresa "
"%(company_name)s."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Semanas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr "¡Le damos la bienvenida a MiCompañía!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr ""
"Bueno, nada dura para siempre, pero ¿está seguro de que quiere desfijar este"
" mensaje?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "What's your name?"
msgstr "¿Cuál es su nombre?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr ""
"Si selecciona \"usuario por defecto\", deberá especificar a un responsable."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"Si se debe guardar una copia original completa de cada correo electrónico "
"como referencia adjunta a cada mensaje procesado. Esto usualmente duplica el"
" tamaño de su base de datos de mensajes."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"Indica si los archivos adjuntos deben ser descargados. Si no se habilita, "
"cualquier archivo adjunto será eliminado de los correos entrantes antes de "
"ser procesado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr "Si desea mostrar todos los destinatarios o solo los importantes."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Write Feedback"
msgstr "Escribir comentarios"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Write your message here..."
msgstr "Escriba su mensaje aquí..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr "Nombre de operación erróneo (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "AAAA-MM-DD HH:MM:SS"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Yeah, pin it!"
msgstr "Sí, fíjalo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Yes, remove it please"
msgstr "Sí, elimínalo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Yesterday"
msgstr "Ayer"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Yesterday:"
msgstr "Ayer:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"Está a punto de dejar esta conversación grupal y no volverá a tener acceso a"
" menos que lo vuelvan a invitar. ¿Está seguro de que desea continuar?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are alone in a private conversation."
msgstr "Se encuentra solo en una conversación privada."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "Está solo en este canal."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are in a private conversation with %(member_names)s."
msgstr "Se encuentra en una conversación privada con %(member_names)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr "Se encuentra en el canal %(bold_start)s#%(channel_name)s%(bold_end)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_service.js:0
#, python-format
msgid "You are no longer following \"%(thread_name)s\"."
msgstr "Ya no está siguiendo \"%(thread_name)s\"."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "No está permitido para usted subir un archivo adjunto aquí."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload attachments on this channel."
msgstr "No tiene permitido subir archivos adjuntos en este canal."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr ""
"Es el administrador de este canal. ¿Está seguro de que desea abandonarlo?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"Puede marcar cualquier mensaje como \"destacado\", y aparecerá en este "
"buzón."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr "No puede escribir en %(field_name)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr "Solo puede chatear con usuarios existentes."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr ""
"Solo puede usar el chat con los contactos que tengan un usuario asignado."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr "Puede ignorar sin problemas este mensaje"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
#, python-format
msgid ""
"You cannot delete %(activity_names)s as it is required in various apps."
msgstr ""
"No puede eliminar %(activity_names)s ya que se utiliza en otras "
"aplicaciones."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"No puede borrar estos grupos, ya que el grupo \"Toda la compañía\" es "
"requerido por otros módulos."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
#, python-format
msgid ""
"You cannot modify %(activities_names)s target model as they are are required"
" in various apps."
msgstr ""
"No puede modificar el modelo de destino %(activities_names)s ya que se "
"utiliza en otras aplicaciones."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr ""
"Solo puede utilizar caracteres latinos sin acento en la dirección del alias "
"%(alias_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr ""
"Solo puede utilizar caracteres latinos sin acento en la dirección del "
"dominio %(domain_name)s."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr "No tiene acceso a"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""
"No tiene derecho a acceder a la lista negra de correos electrónicos. Por "
"favor, póngase en contacto con su administrador."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Ha sido asignado a %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Ha sido asignado/a a"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr "Le han invitado a #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Puede adjuntar archivos a esta plantilla para que se añadan a todos los "
"correos electrónicos creados desde la misma."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "You may not define a template on an abstract model: %s"
msgstr "No puede definir una plantilla en un modelo abstracto: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr ""
"¿Está seguro de que desea fijar este mensaje a %(conversation)s para "
"siempre?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You unpinned your conversation with %s"
msgstr "Desfijó su conversación con %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr "Canceló su suscripción a %s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "You're viewing older messages"
msgstr "Está viendo mensajes más antiguos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr "¡Lo han invitado a un chat!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr "¡Lo han invitado a una reunión!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "You:"
msgstr "Usted:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr "Su"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr ""
"El correo electrónico de su cuenta se ha cambiado de %(old_email)s a "
"%(new_email)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Your account login has been updated"
msgstr "Se ha actualizado el inicio de sesión de su cuenta"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Your account password has been updated"
msgstr "Se ha actualizado la contraseña de su cuenta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr "Su navegador no permite las videoconferencias."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr "Su navegador no permite la activación por voz"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr "Su navegador no permite webRTC"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Your name"
msgstr "Su nombre"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
#, python-format
msgid "[%(name)s] (no email address)"
msgstr "[%(name)s] (sin dirección de correo electrónico)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr "direcciones vinculadas a contactos registrados"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "después de la fecha de finalización"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "después de la fecha límite de actividad anterior"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr "alias %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr "archivo(s) adjunto(s) en este correo electrónico."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "available bitrate:"
msgstr "tasa de bits disponible:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "back"
msgstr "regresar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""
"porque ha intentado\n"
"                contactarle muchas veces en los últimos minutos.\n"
"                <br/>\n"
"                Vuelva a intentarlo más tarde."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "by"
msgstr "por"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "camera"
msgstr "cámara"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"no puede ser procesado. Esta dirección\n"
"    se utiliza para recopilar respuestas y no se debe utilizar para contactar directamente"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "channels"
msgstr "canales"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "clock rate:"
msgstr "frecuencia de reloj:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "close"
msgstr "cerrar"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "codec:"
msgstr "codec:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "created this channel."
msgstr "creó este canal."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "días"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "days overdue:"
msgstr "días de retraso: "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "days:"
msgstr "días: "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr "ensordecido"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__user_device
msgid "devices"
msgstr "dispositivos"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "document"
msgstr "documento"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
#, python-format
msgid "done"
msgstr "hecho"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "down DTLS:"
msgstr "DTLS caído:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "down ICE:"
msgstr "ICE caído:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr "p. ej. \"Discutir propuesta\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr "p. ej. \"Revisar la oferta y hablar sobre los detalles\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr "p. ej. \"Correo de bienvenida\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr ""
"por ejemplo:  \"Le damos la bienvenida a MiCompañía\" o \"Gusto en "
"conocerlo, {{ object.name }}\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr "p. ej. \"rebote\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr "p. ej. \"catchall\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr "p. ej. \"micompañía.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr "p. ej. \"notificaciones\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "p. ej. 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "p. ej. ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr "p. ej. contacto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "p. ej. Discutir propuesta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "p. ej. Incorporación"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "p. ej. programar una reunión"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. domain.com"
msgstr "p. ej. dominio.com"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr "p. ej. soporte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "p. ej. True.True..f"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr "p. ej. user_id"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr "p. ej. \"info@micompañía.odoo.com\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "for"
msgstr "para"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "ha sido creado de:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "ha sido modificado de:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr "acaba de asignar la siguiente actividad:"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr "https://developers.google.com/tenor/guides/content-filtering"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "alias configurado incorrectamente"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "alias configurado incorrectamente (registro de referencia incorrecto)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "invited %s to the channel"
msgstr "invitó %s al canal"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "joined the channel"
msgstr "se unió al canal"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "left the channel"
msgstr "abandonó el canal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "list"
msgstr "lista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "list-item"
msgstr "list-item"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "live"
msgstr "en vivo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "media player Error"
msgstr "error del reproductor multimedia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "microphone"
msgstr "micrófono"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "modelo %s no acepta la creación de documentos"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "meses"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "ms"
msgstr "ms"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr "silenciado"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "new"
msgstr "nuevo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
#, python-format
msgid "no connection"
msgstr "sin conexión"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
#, python-format
msgid "now"
msgstr "ahora"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "el"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "on:"
msgstr "en:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#, python-format
msgid "or"
msgstr "o"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr "otros miembros."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "raising hand"
msgstr "levantar la mano"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""
"responder al documento faltante (%(model)s,%(thread)s), recurrir a la "
"creación del documento"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""
"responder al modelo %s que no acepta la actualización del documento, "
"recurrir a la creación del documento"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "restringido a seguidores"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "restringido a autores conocidos"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "results out of"
msgstr "resultados de"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "screen"
msgstr "pantalla"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr "algunas direcciones específicas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "inmovilizar:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr "modelo de destino no especificado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "template"
msgstr "plantilla"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr "alternar pulsar para hablar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "girar:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "error desconocido"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "modelo de destino desconocido %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "up DTLS:"
msgstr "DTLS en funcionamiento:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "up ICE:"
msgstr "ICE en funcionamiento:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "users"
msgstr "usuarios"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "view"
msgstr "ver"

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr "gestión de mensajes de WebSocket"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "semanas"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "your alias"
msgstr "su alias"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr "“%(member_name)s” en “%(channel_name)s”"
