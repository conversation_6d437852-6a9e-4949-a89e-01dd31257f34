# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* loyalty
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# Wil <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-18 01:16+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__trigger
msgid ""
"\n"
"        Automatic: Customers will be eligible for a reward automatically in their cart.\n"
"        Use a code: Customers will be eligible for a reward if they enter a code.\n"
"        "
msgstr ""
"\n"
"        Automático: los clientes serán elegibles para obtener una recompensa automática en su cesta.\n"
"        Con un código: los clientes serán elegibles para obtener una recompensa si ingresan un código.\n"
"        "

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__available_on
msgid ""
"\n"
"        Manage where your program should be available for use.\n"
"        "
msgstr ""
"\n"
"        Gestione dónde debe estar disponible el uso de su programa.\n"
"        "

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__portal_visible
msgid ""
"\n"
"        Show in web portal, PoS customer ticket, eCommerce checkout, the number of points available and used by reward.\n"
"        "
msgstr ""
"\n"
"        Mostrar en el portal web, factura del cliente en TPV, proceso de pago de comercio electrónico, el número de puntos disponibles y usados por recompensa.\n"
"        "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid " (Max %s)"
msgstr "(Máximo %s)"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "$100"
msgstr "100 €"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%g%% on "
msgstr "%g%% en "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per order"
msgstr "%s por orden"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per order on "
msgstr "%s por pedido sobre "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per point"
msgstr "%s por punto"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per point on "
msgstr "%s por punto en"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "( Max"
msgstr "(Máximo"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "(if at least"
msgstr "(si al menos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "(tax excluded)"
msgstr "(impuestos excluidos)"

#. module: loyalty
#: model:loyalty.program,name:loyalty.10_percent_coupon
msgid "10% Discount Coupons"
msgstr "Cupones de 10 % de descuento"

#. module: loyalty
#: model:loyalty.reward,description:loyalty.10_percent_coupon_reward
#: model:loyalty.reward,description:loyalty.10_percent_with_code_reward
msgid "10% on your order"
msgstr "10 % en su pedido"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "1000"
msgstr "1000"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "2+1 Free"
msgstr "Gratis 2+1"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "2023-08-20"
msgstr "20-08-2023"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "2023-12-31"
msgstr "31-12-2023 "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"<br/>\n"
"                                        <br/>\n"
"\n"
"                                        <span class=\"fw-bold text-decoration-underline\">Applied to:</span>"
msgstr ""
"<br/>\n"
"                                        <br/>\n"
"\n"
"                                        <span class=\"fw-bold text-decoration-underline\">Se aplica a:</span>"

#. module: loyalty
#: model:mail.template,body_html:loyalty.mail_template_gift_card
msgid ""
"<div style=\"background: #ffffff\">\n"
"                <div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                    Here is your gift card!\n"
"                </div>\n"
"                <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                    <img src=\"/loyalty/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\">\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                        <strong t-out=\"format_amount(object.points, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                    <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                        <strong>Gift Card Code</strong>\n"
"                    </p>\n"
"                    <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"                </div>\n"
"                <div t-if=\"object.expiration_date\" style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">Card expires <t t-out=\"format_date(object.expiration_date) or ''\">05/05/2021</t></h3>\n"
"                </div>\n"
"                <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                    <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">Use it right now!</a>\n"
"                    </span>\n"
"                </div>\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"background: #ffffff\">\n"
"                <div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                    ¡Aquí está su tarjeta de regalo!\n"
"                </div>\n"
"                <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                    <img src=\"/loyalty/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\">\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                        <strong t-out=\"format_amount(object.points, object.currency_id) or ''\">150,00 €</strong></h3>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                    <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                        <strong>Código de tarjeta de regalo</strong>\n"
"                    </p>\n"
"                    <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"                </div>\n"
"                <div t-if=\"object.expiration_date\" style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">La tarjeta tiene vigencia hasta el <t t-out=\"format_date(object.expiration_date) or ''\">05/05/2021</t></h3>\n"
"                </div>\n"
"                <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                    <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">¡Úsela ahora!</a>\n"
"                    </span>\n"
"                </div>\n"
"            </div>\n"
"        "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Product Domain\"/>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"Product Domain\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/><span>All Products</span>"
msgstr ""
"<i class=\"fa fa-cube fa-fw\" title=\"Products\"/><span>Todos los "
"productos</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cubes fa-fw\" title=\"Product Categories\"/>"
msgstr "<i class=\"fa fa-cubes fa-fw\" title=\"Product Categories\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-search fa-fw\" title=\"Product Domain\"/>"
msgstr "<i class=\"fa fa-search fa-fw\" title=\"Product Domain\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-tags fa-fw\" title=\"Product Tags\"/>"
msgstr "<i class=\"fa fa-tags fa-fw\" title=\"Product Tags\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Among:</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">Entre:</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Grant</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">Otorgar</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Grant</span>\n"
"                                    <br/>\n"
"                                    the value of the coupon"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">Otorgar</span>\n"
"                                    <br/>\n"
"                                    el valor del cupón"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">In exchange of</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">En lugar de</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"program_type not in ('coupons', 'next_order_coupons')\">Coupons</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'loyalty'\">Loyalty Cards</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type not in ('promotion', 'buy_x_get_y')\">Promos</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'promo_code'\">Discount</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'gift_card'\">Gift Cards</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'ewallet'\">eWallets</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"program_type not in ('coupons', 'next_order_coupons')\">Cupones</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'loyalty'\">Tarjetas de fidalidad</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type not in ('promotion', 'buy_x_get_y')\">Promociones</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'promo_code'\">Descuentos</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'gift_card'\">Tarjetas de regalo</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'ewallet'\">Monederos electrónicos</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid ""
"<span class=\"text-center\">OR</span>\n"
"                                        <br/>"
msgstr ""
"<span class=\"text-center\">O</span>\n"
"                                        <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid ""
"<span colspan=\"2\" invisible=\"program_type != 'coupons'\">Grant the amount"
" of coupon points defined as the coupon value</span>"
msgstr ""
"<span colspan=\"2\" invisible=\"program_type != 'coupons'\">Otrogar la "
"cantidad de puntos de cupón que están definidos como el valor del "
"cupón</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "<span invisible=\"not clear_wallet\"> (or more)</span>"
msgstr "<span invisible=\"not clear_wallet\"> (o más)</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid ""
"<span invisible=\"not will_send_mail\">\n"
"                            Generate and Send \n"
"                        </span>\n"
"                        <span invisible=\"will_send_mail\">\n"
"                            Generate \n"
"                        </span>"
msgstr ""
"<span invisible=\"not will_send_mail\">\n"
"                           Generar y enviar \n"
"                        </span>\n"
"                        <span invisible=\"will_send_mail\">\n"
"                            Generar \n"
"                        </span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "<span> x </span>"
msgstr "<span> x </span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>Minimum purchase of</span>"
msgstr "<span>Compra mínima de</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>Valid for purchase above</span>"
msgstr "<span>Válido para la compra de arriba</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "<span>on</span>"
msgstr "<span>en</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>products</span>"
msgstr "<span>productos</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "<span>tax</span>"
msgstr "<span>impuestos</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "<strong>Gift Card Code</strong>"
msgstr "<strong>Código de tarjeta de regalo</strong>"

#. module: loyalty
#: model:mail.template,body_html:loyalty.mail_template_loyalty_card
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto; background:#ffffff; color:#333333;\"><tbody>\n"
"<tr>\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object._get_mail_partner().name\">\n"
"            Congratulations <t t-out=\"object._get_mail_partner().name or ''\">Brandon Freeman</t>,<br>\n"
"        </t>\n"
"\n"
"        Here is your reward from <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br>\n"
"\n"
"        <t t-foreach=\"object.program_id.reward_ids\" t-as=\"reward\">\n"
"            <t t-if=\"reward.required_points &lt;= object.points\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-esc=\"reward.description\">Reward Description</span>\n"
"                <br>\n"
"            </t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"<tr style=\"margin-top: 16px\">\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        <t t-if=\"object.expiration_date\">\n"
"            before <t t-out=\"object.expiration_date or ''\">2021-06-16</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-foreach=\"object.program_id.rule_ids\" t-as=\"rule\">\n"
"            <t t-if=\"rule.minimum_qty not in [0, 1]\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Minimum purchase of <t t-out=\"rule.minimum_qty or ''\">10</t> products\n"
"                </span><br>\n"
"            </t>\n"
"            <t t-if=\"rule.minimum_amount != 0.00\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Valid for purchase above <t t-out=\"rule.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(rule.minimum_amount) or ''\">10.00</t>\n"
"                </span><br>\n"
"            </t>\n"
"        </t>\n"
"        <br>\n"
"        Thank you,\n"
"        <t t-if=\"object._get_signature()\">\n"
"            <br>\n"
"            <t t-out=\"object._get_signature() or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto; background:#ffffff; color:#333333;\"><tbody>\n"
"<tr>\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object._get_mail_partner().name\">\n"
"            Enhorabuena <t t-out=\"object._get_mail_partner().name or ''\">Brandon Freeman</t>,<br>\n"
"        </t>\n"
"\n"
"        Esta es la recompensa de <t t-out=\"object.program_id.company_id.name or ''\">SuCompañía</t>.<br>\n"
"\n"
"        <t t-foreach=\"object.program_id.reward_ids\" t-as=\"reward\">\n"
"            <t t-if=\"reward.required_points &lt;= object.points\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-esc=\"reward.description\">Descripción de la recompensa</span>\n"
"                <br>\n"
"            </t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"<tr style=\"margin-top: 16px\">\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use el código de promoción\n"
"        <t t-if=\"object.expiration_date\">\n"
"            antes del <t t-out=\"object.expiration_date or ''\">16-06-2021</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-foreach=\"object.program_id.rule_ids\" t-as=\"rule\">\n"
"            <t t-if=\"rule.minimum_qty not in [0, 1]\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    En la compra de mínima de <t t-out=\"rule.minimum_qty or ''\">10</t> productos\n"
"                </span><br>\n"
"            </t>\n"
"            <t t-if=\"rule.minimum_amount != 0.00\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Válido para compras mayores a <t t-out=\"'%0.2f' % float(rule.minimum_amount) or ''\">10.00</t> <t t-out=\"rule.company_id.currency_id.symbol or ''\">€</t>\n"
"                </span><br>\n"
"            </t>\n"
"        </t>\n"
"        <br>\n"
"        Gracias,\n"
"        <t t-if=\"object._get_signature()\">\n"
"            <br>\n"
"            <t t-out=\"object._get_signature() or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"        "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "A coupon with the same code was found."
msgstr "Se encontró un cupón con el mismo código."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_card_card_code_unique
msgid "A coupon/loyalty card must have a unique code."
msgstr "Un cupón/tarjeta de fidelidad debe de tener un código único."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "A program must have at least one reward."
msgstr "Un programa debe tener al menos una recompensa."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "A trigger with the same code as one of your coupon already exists."
msgstr ""
"Ya existe un activador con el mismo código que el que tiene en su cupón."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "ABCDE12345"
msgstr "ABCDE12345"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__active
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_search
msgid "Active"
msgstr "Activo"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_res_partner__loyalty_card_count
#: model:ir.model.fields,field_description:loyalty.field_res_users__loyalty_card_count
msgid "Active loyalty cards"
msgstr "Tarjetas de fidelidad activas "

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Add"
msgstr "Añadir"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Add a reward"
msgstr "Añadir una recompensa"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Add a rule"
msgstr "Añadir una regla"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__all_discount_product_ids
msgid "All Discount Product"
msgstr "Todos los productos de descuento"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Among"
msgstr "Entre"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_generate_wizard__mode__anonymous
msgid "Anonymous Customers"
msgstr "Clientes anónimos"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__mode
msgid "Application"
msgstr "Aplicación"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__applies_on
msgid "Applies On"
msgstr "Aplica en"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_search
msgid "Archived"
msgstr "Archivado"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_mail__trigger__create
msgid "At Creation"
msgstr "Al crear"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__trigger__auto
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__mode__auto
msgid "Automatic"
msgstr "Automático"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Automatic promo: 10% off on orders higher than $50"
msgstr "Promoción automática: 10 % de descuento en pedidos mayores a 50 €"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__available_on
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Available On"
msgstr "Disponible en"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Balance"
msgstr "Saldo"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Barcode"
msgstr "Código de barras"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Buy 10 products to get 10$ off on the 11th one"
msgstr "Compre 10 productos para obtener un 10 % de descuento en el 11°"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Buy 2 products and get a third one for free"
msgstr "Compre 2 productos y obtenga el tercero gratis"

#. module: loyalty
#: model:loyalty.program,name:loyalty.3_cabinets_plus_1_free
msgid "Buy 3 large cabinets, get one for free"
msgstr "Compre 3 gabinetes grandes, reciba uno gratis"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__buy_x_get_y
#, python-format
msgid "Buy X Get Y"
msgstr "Comprar X y recibir Y"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
#, python-format
msgid "Can not generate coupon, no program is set."
msgstr "No se pudo generar un cupón, no hay un programa configurado."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Card expires"
msgstr "La tarjeta expira"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_category_id
msgid "Categories"
msgstr "Categorías"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__cheapest
msgid "Cheapest Product"
msgstr "Producto más barato"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__clear_wallet
msgid "Clear Wallet"
msgstr "Limpiar monedero"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Clear all promo point(s)"
msgstr "Eliminar todos los puntos de promoción"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__code
msgid "Code"
msgstr "Código"

#. module: loyalty
#: model:loyalty.program,name:loyalty.10_percent_with_code
msgid "Code for 10% on orders"
msgstr "Código para 10 % en pedidos"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__communication_plan_ids
msgid "Communication Plan"
msgstr "Plan de comunicación"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Communications"
msgstr "Comunicaciones"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__company_id
msgid "Company"
msgstr "Compañía"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "Compose Email"
msgstr "Redactar correo electrónico"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__rule_ids
msgid "Conditional rules"
msgstr "Reglas condicionales"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Conditions"
msgstr "Condiciones"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Congratulations"
msgstr "¡Enhorabuena!"

#. module: loyalty
#: model:ir.model,name:loyalty.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "Botones del panel de control"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_ids
#, python-format
msgid "Coupon"
msgstr "Cupón"

#. module: loyalty
#: model:ir.actions.report,name:loyalty.report_loyalty_card
msgid "Coupon Code"
msgstr "Código de cupón"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_count
msgid "Coupon Count"
msgstr "Número de cupones"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Coupon point(s)"
msgstr "Punto(s) de cupón"

#. module: loyalty
#: model:loyalty.program,portal_point_name:loyalty.10_percent_coupon
msgid "Coupon points"
msgstr "Puntos de cupón"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Coupon value"
msgstr "Valor del cupón"

#. module: loyalty
#: model:mail.template,name:loyalty.mail_template_loyalty_card
msgid "Coupon: Coupon Information"
msgstr "Cupón: información del cupón"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.actions.act_window,name:loyalty.loyalty_card_action
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__coupons
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
#, python-format
msgid "Coupons"
msgstr "Cupones"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_gift_ewallet_action
msgid "Create a new one from scratch, or use one of the templates below."
msgstr "Cree una desde cero o use una de las siguientes plantillas."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_discount_loyalty_action
msgid "Create one from scratch, or use a templates below:"
msgstr "Cree una desde cero o use una de las siguientes plantillas: "

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Create record"
msgstr "Crear registro"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__create_date
msgid "Created on"
msgstr "Creado el"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,portal_point_name:loyalty.3_cabinets_plus_1_free
#, python-format
msgid "Credit(s)"
msgstr "Crédito(s)"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__currency_symbol
msgid "Currency sign, to be used when printing amounts."
msgstr "Símbolo de la moneda, para ser usado cuando se impriman informes."

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__both
msgid "Current & Future orders"
msgstr "Pedidos actuales y futuros"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__current
msgid "Current order"
msgstr "Pedido actual"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__customer_tag_ids
msgid "Customer Tags"
msgstr "Etiquetas del cliente"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__customer_ids
msgid "Customers"
msgstr "Clientes"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "DEMO_CODE"
msgstr "DEMO_CODE"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "DEMO_TEXT"
msgstr "DEMO_TEXT"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Define Discount codes on conditional rules then share it with your customers"
" for rewards."
msgstr ""
"Defina códigos de descuento en reglas condicionales y después compártalo con"
" sus clientes para obtener recompensas."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__description
msgid "Description"
msgstr "Descripción"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Description on order"
msgstr "Descripción en la orden"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__reward_type__discount
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Discount"
msgstr "Descuento"

#. module: loyalty
#: model:ir.actions.act_window,name:loyalty.loyalty_program_discount_loyalty_action
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Discount & Loyalty"
msgstr "Descuento y fidelidad"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_applicability
msgid "Discount Applicability"
msgstr "Vigencia del descuento"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__promo_code
msgid "Discount Code"
msgstr "Código de descuento"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_line_product_id
msgid "Discount Line Product"
msgstr "Producto de la línea de descuento"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_mode
msgid "Discount Mode"
msgstr "Modo de descuento"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__payment_program_discount_product_id
msgid "Discount Product"
msgstr "Product de descuento"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_domain
msgid "Discount Product Domain"
msgstr "Dominio del producto de descuento"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__code
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
#, python-format
msgid "Discount code"
msgstr "Código de descuento"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,portal_point_name:loyalty.10_percent_with_code
#, python-format
msgid "Discount point(s)"
msgstr "Punto(s) de descuento"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Discount product"
msgstr "Producto de descuento"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_category_id
msgid "Discounted Prod. Categories"
msgstr "Categorías de los productos con descuento"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_tag_id
msgid "Discounted Prod. Tag"
msgstr "Etiquetas de los productos con descuento"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_ids
msgid "Discounted Products"
msgstr "Productos con descuento"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Discounts"
msgstr "Descuentos"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Displayed as"
msgstr "Mostrado como"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Drive repeat purchases by sending a unique, single-use coupon code for the "
"next purchase when a customer buys something in your store."
msgstr ""
"Para impulsar compras repetidas, envíe un código de cupón único y de un solo"
" uso para la próxima compra cuando un cliente compre algo en su tienda."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Each rule can grant points to the customer he will be able to exchange "
"against rewards"
msgstr ""
"Cada regla le otorgará puntos al cliente que después podrá intercambiar por "
"recompensas."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__mail_template_id
msgid "Email Template"
msgstr "Plantilla de correo electrónico"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__mail_template_id
msgid "Email template"
msgstr "Plantillas de correo electrónico"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__date_to
msgid "End date"
msgstr "Fecha de finalización"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__minimum_amount_tax_mode__excl
msgid "Excluded"
msgstr "Excluido"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__expiration_date
msgid "Expiration Date"
msgstr "Fecha de expiración"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "Expiration date cannot be set on a loyalty card."
msgstr ""
"No se puede establecer la fecha de vencimiento en una tarjeta de fidelidad."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Fidelity Card"
msgstr "Tarjeta de fidelidad"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Fidelity Cards"
msgstr "Tarjetas de fidelidad"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Fill in your eWallet, to pay future orders"
msgstr "Recargue su monedero electrónico y úselo para pagar pedidos futuras"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__mode
msgid "For"
msgstr "Para"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__reward_type__product
#, python-format
msgid "Free Product"
msgstr "Producto gratuito"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Free Product - %s"
msgstr "Producto gratuito - %s"

#. module: loyalty
#: model:loyalty.reward,description:loyalty.3_cabinets_plus_1_free_reward
msgid "Free Product - Large Cabinet"
msgstr "Producto gratuito - Gabinete grande"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Free Product - [%s]"
msgstr "Producto gratuito - [%s]"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "Free product"
msgstr "Producto gratuito"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__future
msgid "Future orders"
msgstr "Pedidos futuros"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#: model:ir.actions.act_window,name:loyalty.loyalty_generate_wizard_action
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
#, python-format
msgid "Generate"
msgstr "Generar"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Generate &amp; share coupon codes manually. It can be used in eCommerce, "
"Point of Sale or regular orders to claim the Reward. You can define "
"constraints on its usage through conditional rule."
msgstr ""
"Genere y comparta cupones manualmente. Se puede usar en comercio "
"electrónico, punto de venta o pedidos regulares para obtener la recompensa. "
"Puede definir limitaciones sobre el uso con una regla condicional."

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_generate_wizard
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate Coupons"
msgstr "Generar cupones"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate Gift Cards"
msgstr "Generar tarjetas de regalo"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Generate and share unique coupons with your customers"
msgstr "Genere y comparta cupones únicos con sus clientes"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate eWallet"
msgstr "Generar monedero electrónico"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Get 10% off on some products, with a code"
msgstr "Obtenga 10 % de descuento en algunos productos con un código "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_gift_ewallet_view_form
msgid "Gift &amp; Ewallet"
msgstr "Regalo y monedero electrónico"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.actions.report,name:loyalty.report_gift_card
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__gift_card
#: model:loyalty.reward,description:loyalty.gift_card_program_reward
#: model:product.template,name:loyalty.gift_card_product_50_product_template
#, python-format
msgid "Gift Card"
msgstr "Tarjeta de regalo"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Gift Card Products"
msgstr "Productos de tarjeta de regalo"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Gift Card value"
msgstr "Valor de la tarjeta de regalo"

#. module: loyalty
#: model:mail.template,name:loyalty.mail_template_gift_card
msgid "Gift Card: Gift Card Information"
msgstr "Tarjeta de regalo: información de tarjeta de regalo"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,name:loyalty.gift_card_program
#, python-format
msgid "Gift Cards"
msgstr "Tarjetas de regalo"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Gift Cards are created manually or automatically sent by email when the customer orders a gift card product.\n"
"                                    <br/>\n"
"                                    Then, Gift Cards can be used to pay orders."
msgstr ""
"Las tarjetas de regalo se crean manualmente o se envían automáticamente por correo cuando el cliente pide un producto de tarjeta de regalo.\n"
"                                    <br/>\n"
"                                    Después, las tarjetas de regalo se pueden usar para pagar pedidos."

#. module: loyalty
#: model:ir.actions.act_window,name:loyalty.loyalty_program_gift_ewallet_action
msgid "Gift cards & eWallet"
msgstr "Tarjetas de regalo y monedero electrónico"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__points_granted
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Grant"
msgstr "Otorgar"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Grant 1 credit for each item bought then reward the customer with Y items in"
" exchange of X credits."
msgstr ""
"Otorgue 1 crédito por cada artículo comprado y después regálele al cliente Y"
" artículos a cambio de X créditos."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Here is your gift card!"
msgstr "¡Aquí está su tarjeta de regalo!"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Here is your reward from"
msgstr "Aquí está su recompensa de"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__id
msgid "ID"
msgstr "ID"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "If minimum"
msgstr "Con al menos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "In exchange of"
msgstr "A cambio de"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_search
msgid "Inactive"
msgstr "Inactivo"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__minimum_amount_tax_mode__incl
msgid "Included"
msgstr "Incluido"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
#, python-format
msgid "Invalid quantity."
msgstr "Cantidad no válida"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__is_global_discount
msgid "Is Global Discount"
msgstr "Es un descuento global"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__is_nominative
msgid "Is Nominative"
msgstr "Es nominativo"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__is_payment_program
msgid "Is Payment Program"
msgstr "Es un programa de pago"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_count_display
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_tree
msgid "Items"
msgstr "Artículos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "John Doe"
msgstr "John Doe"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_mail_view_tree
msgid "Limit"
msgstr "Límite"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__limit_usage
msgid "Limit Usage"
msgstr "Límite de uso"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Logo"
msgstr "Logotipo"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Loyalty Card"
msgstr "Tarjeta de fidelidad "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.res_partner_form
#, python-format
msgid "Loyalty Cards"
msgstr "Tarjetas de fidelidad"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_mail
msgid "Loyalty Communication"
msgstr "Comunicación de fidelización"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Cupón de fidelidad"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "Programa de fidelización"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "Recompensa de fidelidad"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Regla de fidelidad"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Loyalty point(s)"
msgstr "Punto(s) de fidelidad"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_max_amount
msgid "Max Discount"
msgstr "Descuento máximo"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__max_usage
msgid "Max Usage"
msgstr "Uso máximo"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_program_check_max_usage
msgid "Max usage must be strictly positive if a limit is used."
msgstr "Si se usa un límite, el uso máximo debe ser estrictamente positivo."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_amount_tax_mode
msgid "Minimum Amount Tax Mode"
msgstr "Modo de impuestos de impuesto mínimo"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_amount
msgid "Minimum Purchase"
msgstr "Compra mínima"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_qty
msgid "Minimum Quantity"
msgstr "Cantidad mínima"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__multi_product
msgid "Multi Product"
msgstr "Productos múltiples"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Next Order Coupon"
msgstr "Cupón del próximo pedido"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__next_order_coupons
#, python-format
msgid "Next Order Coupons"
msgstr "Cupones del próximo pedido"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_card_action
msgid "No Coupons Found."
msgstr "No se encontraron cupones."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_gift_ewallet_action
msgid "No loyalty program found."
msgstr "No se encontraron programas de fidelización."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_discount_loyalty_action
msgid "No program found."
msgstr "No se encontraron programas."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Odoo"
msgstr "Odoo"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__order
msgid "Order"
msgstr "Pedido"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Point(s)"
msgstr "Punto(s)"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__points
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__points
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Points"
msgstr "Puntos"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__points_display
msgid "Points Display"
msgstr "Mostrar puntos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Points Unit"
msgstr "Unidad de puntos"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__required_points
msgid "Points needed"
msgstr "Puntos necesarios"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__points_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__portal_point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_name
msgid "Portal Point Name"
msgstr "Nombre de punto del portal"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__portal_visible
msgid "Portal Visible"
msgstr "Portal visible"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_pricelist
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__pricelist_ids
msgid "Pricelist"
msgstr "Lista de precios"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_template
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_id
msgid "Product"
msgstr "Producto"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_domain
msgid "Product Domain"
msgstr "Dominio del producto"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_tag_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_tag_id
msgid "Product Tag"
msgstr "Etiqueta del producto"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_product
msgid "Product Variant"
msgstr "Variante de producto"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__payment_program_discount_product_id
msgid "Product used in the sales order to apply the discount."
msgstr "Producto utilizado en el pedido de venta para aplicar el descuento."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__discount_line_product_id
msgid ""
"Product used in the sales order to apply the discount. Each reward has its "
"own product for reporting purpose"
msgstr ""
"Producto usado en pedidos de venta para aplicar el descuento. Cada "
"recompensa tiene su propio producto y propósito de informe."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__trigger_product_ids
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_ids
msgid "Products"
msgstr "Productos"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__program_id
msgid "Program"
msgstr "Programa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__name
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Program Name"
msgstr "Nombre del programa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__program_type
msgid "Program Type"
msgstr "Tipo de programa"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Program trigger"
msgstr "Activador del programa"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promo Code"
msgstr "Código promocional"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promo point(s)"
msgstr "Punto(s) de promoción"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promos"
msgstr "Promociones"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promotional Program"
msgstr "Programa promocional"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__promotion
msgid "Promotions"
msgstr "Promociones"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__coupon_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Quantity rewarded"
msgstr "Cantidad recompensada"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Quantity to generate"
msgstr "Cantidad a generar"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_amount
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Reward"
msgstr "Recompensa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_mode
msgid "Reward Point Mode"
msgstr "Modo de puntos de recompensa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_domain
msgid "Reward Product Domain"
msgstr "Dominio del producto de recompensa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_qty
msgid "Reward Product Qty"
msgstr "Ctd. de productos de recompensa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_uom_id
msgid "Reward Product Uom"
msgstr "UdM del producto de recompensa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_ids
msgid "Reward Products"
msgstr "Productos de recompensa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_type
msgid "Reward Type"
msgstr "Tipo de recompensa"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__reward_ids
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Rewards"
msgstr "Recompensas"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_rule_reward_point_amount_positive
msgid "Rule points reward must be strictly positive."
msgstr ""
"Las reglas de los puntos de recompensa deben ser estrictamente positivas."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Rules & Rewards"
msgstr "Reglas y recompensas"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_generate_wizard__mode__selected
msgid "Selected Customers"
msgstr "Clientes seleccionados"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Sell Gift Cards, that allows to purchase products"
msgstr "Vender tarjetas de regalo que se pueden usar para comprar productos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Send"
msgstr "Enviar"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Send a coupon after an order, valid for next purchase"
msgstr "Enviar un cupón después de un pedido, válido para la próxima compra"

#. module: loyalty
#: model:mail.template,description:loyalty.mail_template_gift_card
msgid "Sent to customer who purchased a gift card"
msgstr "Enviado al cliente que compró una tarjeta de regalo"

#. module: loyalty
#: model:mail.template,description:loyalty.mail_template_loyalty_card
msgid "Sent to customer with coupon information"
msgstr "Enviado al cliente con información del cupón"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Set up conditional rules on the order that will give access to rewards for "
"customers"
msgstr ""
"Configurar reglas condicionales en el pedido que le dará acceso a las "
"recompensas para clientes"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Show points Unit"
msgstr "Mostrar unidad de puntos"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__specific
msgid "Specific Products"
msgstr "Productos específicos"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_split
msgid "Split per unit"
msgstr "Dividir por unidad"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "Split per unit is not allowed for Loyalty and eWallet programs."
msgstr ""
"Divida por unidad no está disponible para programas de fidelización y de "
"monedero electrónico."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__date_from
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__currency_symbol
msgid "Symbol"
msgstr "Símbolo"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Thank you,"
msgstr "Gracias, "

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_discount_positive
msgid "The discount must be strictly positive."
msgstr "El descuento debe ser estrictamente positivo."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__date_to
msgid "The end date is included in the validity period of this program"
msgstr ""
"La fecha de finalización se incluye en el periodo de validez de este "
"programa "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid ""
"The loyalty program's currency must be the same as all it's pricelists ones."
msgstr ""
"La moneda del programa de fidelización debe ser la misma que está "
"establecida en todas las listas de precios. "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "The promo code must be unique."
msgstr "El código promocional debe ser único."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_required_points_positive
msgid "The required points for a reward must be strictly positive."
msgstr ""
"Los puntos requeridos para una recompensa deben ser estrictamente positivos."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "The reward description field cannot be empty."
msgstr "El campo con la descripción de la recompensa no puede estar vacío."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_product_qty_positive
msgid "The reward product quantity must be strictly positive."
msgstr ""
"La cantidad del producto de recompensa debe ser estrictamente positivo."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__date_from
msgid "The start date is included in the validity period of this program"
msgstr ""
"La fecha de inicio está incluida en el periodo de validez de este programa"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid ""
"The validity period's start date must be anterior or equal to its end date."
msgstr ""
"La fecha de inicio del periodo de validez debe ser anterior o igual a la "
"fecha de finalización "

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_card_action
msgid "There haven't been any coupons generated yet."
msgstr "Todavía no hay cupones generados."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__reward_product_ids
msgid "These are the products that can be claimed with this rule."
msgstr "Estos son los productos que se pueden reclamar con esta regla."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__discount_max_amount
msgid ""
"This is the max amount this reward may discount, leave to 0 for no limit."
msgstr ""
"Esta es la cantidad máxima que esta recompensa puede descontar. Deje 0 para "
"que no tenga límite."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_pricelist.py:0
#, python-format
msgid ""
"This pricelist may not be archived. It is being used for active promotion "
"programs: %s"
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_product.py:0
#, python-format
msgid ""
"This product may not be archived. It is being used for an active promotion "
"program."
msgstr ""
"Este producto no se puede archivar ya que se está usando para un programa de"
" ofertas activo."

#. module: loyalty
#: model:product.template,name:loyalty.ewallet_product_50_product_template
msgid "Top-up eWallet"
msgstr "Recargar monedero electrónico"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__total_order_count
msgid "Total Order Count"
msgstr "Número total de pedidos"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__trigger
msgid "Trigger"
msgstr "Activador"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Unsupported search operator"
msgstr "Operador de búsqueda no compatible"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__use_count
msgid "Use Count"
msgstr "Número de usos"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__trigger__with_code
msgid "Use a code"
msgstr "Usar un código"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Use points on"
msgstr "Usar puntos en"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Use this promo code before"
msgstr "Usa este código promocional antes de"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__user_has_debug
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__user_has_debug
msgid "User Has Debug"
msgstr "El usuario tiene depuración"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__valid_until
msgid "Valid Until"
msgstr "Válido hasta"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__trigger
msgid "When"
msgstr "Cuándo"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_mail__trigger__points_reach
msgid "When Reaching"
msgstr "Al alcanzar"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"When customers make an order, they accumulate points they can exchange for "
"rewards on the current order or on a future one."
msgstr ""
"Cuando los clientes hacen un pedido, acumulan puntos que pueden canjear por "
"recompensas en su pedido actual o uno futuro."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"When generating coupon, you can define a specific points value that can be "
"exchanged for rewards."
msgstr ""
"Al generar un cupón puede definir un valor de puntos específico que se puede"
" intercambiar por recompensas."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_rule__reward_point_split
msgid ""
"Whether to separate reward coupons per matched unit, only applies to "
"'future' programs and trigger mode per money spent or unit paid.."
msgstr ""
"Si separar los cupones de recompensa por unidad emparejada solo se aplica "
"para programas 'futuros' y activar el modo por dinero gastado o unidad "
"pagada."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__will_send_mail
msgid "Will Send Mail"
msgstr "Se enviará correo electrónico"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Win points with each purchase, and claim gifts"
msgstr "Gane puntos con cada compra y obtenga regalos"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__mode__with_code
msgid "With a promotion code"
msgstr "Con un código de promoción"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "You can not delete a program in an active state"
msgstr "No puede eliminar un programa en estado activo"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_product.py:0
#: code:addons/loyalty/models/product_template.py:0
#, python-format
msgid ""
"You cannot delete %(name)s as it is used in 'Coupons & Loyalty'. Please "
"archive it instead."
msgstr ""
"No puede eliminar %(name)s ya que se utiliza en \"Cupones y fidelidad\". Por"
" favor, archívelo en su lugar."

#. module: loyalty
#: model:mail.template,subject:loyalty.mail_template_gift_card
msgid "Your Gift Card at {{ object.company_id.name }}"
msgstr "Su tarjeta de regalo de {{ object.company_id.name }}"

#. module: loyalty
#: model:mail.template,subject:loyalty.mail_template_loyalty_card
msgid "Your reward coupon from {{ object.program_id.company_id.name }} "
msgstr "Su cupón de recompensa de {{ object.program_id.company_id.name }}"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "all"
msgstr "todos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "discount"
msgstr "descuento"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "e.g. 10% discount on laptops"
msgstr "p. ej. 10 % de descuento en portátiles"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__ewallet
#, python-format
msgid "eWallet"
msgstr "Monedero electrónico"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "eWallet Products"
msgstr "Productos de monedero electrónico"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "eWallet value"
msgstr "Valor de monedero electrónico"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "eWallets"
msgstr "Monederos electrónicos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"eWallets are created manually or automatically when the customer orders a eWallet product.\n"
"                                    <br/>\n"
"                                    Then, eWallets are proposed during the checkout, to pay orders."
msgstr ""
"Los monederos electrónicos se crean de forma manual o automática cuando el cliente pide un producto de monedero electrónico.\n"
"                                    <br/>\n"
"                                    Después, se propondrá el monedero electrónico como método de pago de los pedidos."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "item(s) bought"
msgstr "artículo(s) comprado(s)"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "loyalty Reward"
msgstr "recompensa de fidelidad"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"on the cheapest product\n"
"                                        <br/>"
msgstr ""
"en el producto más barato\n"
"                                        <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "on your next order"
msgstr "en su próximo pedido"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"on your order\n"
"                                        <br/>"
msgstr ""
"en su pedido\n"
"                                        <br/>"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per %s spent"
msgstr "por %s gastados"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per order"
msgstr "por pedido"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per unit paid"
msgstr "por unidad pagada"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "specific products"
msgstr "productos específicos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "spent"
msgstr "gastado(s)"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "the cheapest product"
msgstr "el producto más barato"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "to"
msgstr "a"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "usages"
msgstr "usos"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "used)"
msgstr "utilizados)"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "your order"
msgstr "su pedido"
