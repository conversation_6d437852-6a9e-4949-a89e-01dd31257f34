# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * gamification_sale_crm
#
# Translators:
# Wil Odoo, 2023
# <PERSON> <<EMAIL>>, 2025
# "<PERSON><PERSON><PERSON> (mfar)" <<EMAIL>>, 2025.
# <PERSON><PERSON> <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2025-08-16 01:11+0000\n"
"Last-Translator: \"<PERSON><PERSON><PERSON> (mfar)\" <<EMAIL>>\n"
"Language-Team: Spanish (Latin America) <https://translate.odoo.com/projects/"
"odoo-17/gamification_sale_crm/es_419/>\n"
"Language: es_419\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n != 0 && n % 1000000 == 0)"
" ? 1 : 2);\n"
"X-Generator: Weblate 5.12.2\n"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_customer_refunds
msgid "Customer Credit Notes"
msgstr "Notas de crédito del cliente"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_lead_delay_close
msgid "Days to Close a Deal"
msgstr "Días para cerrar un trato"

#. module: gamification_sale_crm
#: model:gamification.challenge,name:gamification_sale_crm.challenge_crm_marketing
msgid "Lead Acquisition"
msgstr "Adquisición de leads"

#. module: gamification_sale_crm
#: model:gamification.challenge,name:gamification_sale_crm.challenge_crm_sale
msgid "Monthly Sales Targets"
msgstr "Objetivos de venta mensuales"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_new_leads
msgid "New Leads"
msgstr "Nuevos leads"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_new_opportunities
msgid "New Opportunities"
msgstr "Nuevas oportunidades"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_sale_order_created
msgid "New Sales Orders"
msgstr "Nuevas órdenes de venta"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_paid_sale_order
msgid "Paid Sales Orders"
msgstr "Órdenes de venta pagadas"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_lead_delay_open
msgid "Time to Qualify a Lead"
msgstr "Tiempo para calificar un lead"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_tot_customer_refunds
msgid "Total Customer Credit Notes"
msgstr "Total de notas de crédito del cliente"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_tot_invoices
msgid "Total Invoiced"
msgstr "Total facturado"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_tot_paid_sale_order
msgid "Total Paid Sales Orders"
msgstr "Total de órdenes de venta pagadas"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_lead_delay_close
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_lead_delay_open
msgid "days"
msgstr "días"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_customer_refunds
msgid "invoices"
msgstr "facturas de clientes"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_new_leads
msgid "leads"
msgstr "leads"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_new_opportunities
msgid "opportunities"
msgstr "oportunidades"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_paid_sale_order
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_sale_order_created
msgid "orders"
msgstr "órdenes"
