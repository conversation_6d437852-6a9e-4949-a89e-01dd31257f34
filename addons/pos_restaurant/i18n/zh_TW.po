# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_restaurant
#
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON>, 2025
#
# "<PERSON> (ngto)" <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2025-08-15 01:10+0000\n"
"Last-Translator: \"<PERSON> (ngto)\" <<EMAIL>>\n"
"Language-Team: Chinese (Traditional Han script) <https://translate.odoo.com/"
"projects/odoo-17/pos_restaurant/zh_Hant/>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>樓層名稱: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sales: </strong>"
msgstr "<strong>銷售點:</strong>"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""
"餐廳樓層代表服務客戶的位置，\n"
"                您可以定義和佈置餐桌。"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "啟用"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Add"
msgstr "加入"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Add Floor"
msgstr "加入樓層"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Add a new floor to get started."
msgstr "新增一個樓層，即可開始。"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "增加新的餐廳樓層"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Add a new table to get started."
msgstr "新增一張餐桌，即可開始。"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Add a tip"
msgstr "增加小費"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/orderline_note_button/orderline_note_button.js:0
#, python-format
msgid "Add internal Note"
msgstr "加入內部備註"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add internal notes on order lines for the kitchen"
msgstr "在廚房單加入內部備註"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment"
msgstr "付款後加入小費"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#, python-format
msgid "Adjust Amount"
msgstr "調整金額"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr "調整付款終端機授權的金額，以便在顧客離開後或營業時間結束時，加入小費。"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr "允許分拆賬單"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_iface_orderline_notes
msgid "Allow custom Internal notes on Orderlines."
msgstr "允許在訂單資料行自訂內部備註。"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr "允許付款前列印收據"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "允許在付款前列印帳單。"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr "該餐桌的內部識別碼"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "外觀"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "已封存"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Are you sure?"
msgstr "確定執行此操作？"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/back_button/back_button.xml:0
#, python-format
msgid "BACK"
msgstr "返回"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Back"
msgstr "返回"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "背景顏色"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "背景圖片"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "培根漢堡"

#. module: pos_restaurant
#: model:pos.payment.method,name:pos_restaurant.payment_method
msgid "Bank"
msgstr "銀行"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Bar"
msgstr "條"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/print_bill_button/print_bill_button.xml:0
#, python-format
msgid "Bill"
msgstr "帳單"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "帳單列印中"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "帳單拆分中"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/table_guests_button/table_guests_button.js:0
#, python-format
msgid "Blocked action"
msgstr "已封鎖操作"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Blue"
msgstr "藍色"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/product_screen.xml:0
#, python-format
msgid "Book table"
msgstr "訂枱"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.burger_drink_combo_product_template
msgid "Burger Menu Combo"
msgstr "漢堡餐牌組合"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash Restaurant"
msgstr "現金餐廳"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/back_button/back_button.xml:0
#, python-format
msgid "Change table"
msgstr "轉枱"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "乳酪漢堡"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr "雞肉咖喱三明治"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Close"
msgstr "關閉"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
#, python-format
msgid "Close Tab"
msgstr "關閉標籤"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr "俱樂部三明治"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "可口可樂"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "顏色"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Copy"
msgstr "複製"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "建立於"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Delete"
msgstr "刪除"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Delete Error"
msgstr "刪除錯誤"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr "設計樓層並將訂單分配給餐桌"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/table_guests_button/table_guests_button.xml:0
#, python-format
msgid "Dine-in Guests"
msgstr "堂食顧客"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "飲品"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr "提早列印收據"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
#, python-format
msgid "Edit Plan"
msgstr "編輯平面圖"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "在POS中啟用帳單拆分。"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.espresso_product_template
msgid "Espresso"
msgstr "特濃咖啡"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.fanta_product_template
msgid "Fanta"
msgstr "芬達"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Fill"
msgstr "實色"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "樓層"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "樓層名稱"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Floor Name ?"
msgstr "樓層名稱？"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "樓面圖"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Floor name"
msgstr "樓層名稱"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr "樓層: %s - PoS 設定: %s \n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr "樓層"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr "樓層及餐桌平面圖"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "食品"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#, python-format
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr "為方便起見，我們提供以下小費計算:"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr "菇類"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Green"
msgstr "綠色"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.green_tea_product_template
msgid "Green Tea"
msgstr "綠茶"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Grey"
msgstr "灰色"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
msgid "Guests"
msgstr "顧客"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#, python-format
msgid "Guests:"
msgstr "顧客:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/table_guests_button/table_guests_button.js:0
#, python-format
msgid "Guests?"
msgstr "賓客?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "高"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "水平位置"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "識別號"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.ice_tea_product_template
msgid "Ice Tea"
msgstr "冰茶"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr "如果為 false，餐桌將會被取消而且會在POS停用"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/orderline_note_button/orderline_note_button.xml:0
#, python-format
msgid "Internal Note"
msgstr "內部備註"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Internal Note added by the waiter."
msgstr "服務生添加的內部備註。"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_orderline_notes
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Internal Notes"
msgstr "內部備註"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "為酒吧/餐廳"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
#, python-format
msgid "Keep Open"
msgstr "保持開啟"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Light grey"
msgstr "淺灰色"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Loading"
msgstr "載入中⋯"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr "午餐壽司卷 18 件"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr "午餐鮭魚 20 件"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr "午餐手卷組合 3 件"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr "瑪格麗特"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.milkshake_banana_product_template
msgid "Milkshake Banana"
msgstr "奶昔香蕉"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "美粒果"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr "馬蘇里拉三明治"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "New Floor"
msgstr "新樓層"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "No Tip"
msgstr "沒有小費"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Number of Seats?"
msgstr "座位數目？"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#, python-format
msgid "Ok"
msgstr "確定"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Online reservation for restaurant"
msgstr "網上預訂餐廳"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Oops! No floors available."
msgstr "哎唷！沒有樓層可用。"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Oops! No tables available."
msgstr "哎唷！沒有餐桌可用。"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Open"
msgstr "開啟"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Orange"
msgstr "橙色"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Order"
msgstr "訂單"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#, python-format
msgid "PRO FORMA"
msgstr "備考"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr "四重芝士意粉"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr "番茄肉醬意粉"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Pay"
msgstr "付款"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Payment"
msgstr "付款"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS設定"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "銷售點訂單資料行"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "POS訂單"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "POS付款"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr "POS 操作時段"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_ids
msgid "Point of Sales"
msgstr "POS營業點"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr "銷售點介面列印賬單"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr "銷售點介面分拆賬單"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr "銷售點付款後設置小費"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#, python-format
msgid "Print"
msgstr "列印"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Purple"
msgstr "紫色"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Red"
msgstr "紅色"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Removing a floor cannot be undone. Do you still want to remove %s?"
msgstr "移除樓層的操作無法還原。確定仍要移除 %s？"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr "移除餐桌的操作無法還原"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Rename"
msgstr "重新命名"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Reprint receipts"
msgstr "重印收據"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "餐廳樓層"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "餐廳樓層"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "餐桌"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#, python-format
msgid "Reverse"
msgstr "反向"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "反向付款"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "圓"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Round Shape"
msgstr "圓形形狀"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "鮭魚和酪梨"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "請儲存變更，然後返回此處，進一步設定功能。"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.schweppes_product_template
msgid "Schweppes"
msgstr "玉泉汽水"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr "座位"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "序列號"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr "付款後設定小費"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Settle"
msgstr "解決"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
#, python-format
msgid "Shape"
msgstr "圖形"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "Signature"
msgstr "簽名"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr "辣鮪魚三明治"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/split_bill_button/split_bill_button.xml:0
#, python-format
msgid "Split"
msgstr "拆分"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr "可分拆訂單總額或個別資料行"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "方形"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Square Shape"
msgstr "方形形狀"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "Subtotal"
msgstr "小計"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
#, python-format
msgid "Switch Floor View"
msgstr "切換平面圖檢視"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
#, python-format
msgid "Table"
msgstr "餐桌"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant_appointment
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_module_pos_restaurant_appointment
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Table Booking"
msgstr "訂枱"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr "餐桌名稱"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Table Name?"
msgstr "枱號?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Table is not empty"
msgstr "餐桌仍在佔用"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "餐桌"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "本訂單接待的客人總數."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr "兼容html格式的樓層背景顏色"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "本桌預設的可服務客戶數."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "此POS營業點服務的餐廳樓層。"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid ""
"The table already contains an order. Do you want to proceed and transfer the"
" order here?"
msgstr "這餐桌已經有點單了，您要繼續並將訂單轉移到這裡嗎?"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "本訂單所服務的餐桌"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr "餐桌顏色，以 CSS 屬性 background 的有效數值表示"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "餐桌顯示高度，以像素 px 表示"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr "餐桌的橫向位置－從左側到餐桌中心位置, 以像素點表示"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr "餐桌的垂直位置－從頂部到餐桌中心位置, 以像素點表示"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "餐桌的寬度以像素點顯示"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr "這個訂單還沒有同步到服務器。請確認它已被同步,然後再試。"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Tint"
msgstr "色調"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Tip"
msgstr "小費"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Tip Amount"
msgstr "提示金額"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "Tip:"
msgstr "小費:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Tipping"
msgstr "小費"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "Total:"
msgstr "總計:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/transfer_order_button/transfer_order_button.xml:0
#, python-format
msgid "Transfer"
msgstr "轉移"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Turquoise"
msgstr "藍綠色"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
#, python-format
msgid "Unsynced order"
msgstr "未同步的訂單"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Username"
msgstr "使用者名稱"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr "素食"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "垂直位置"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "水"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "White"
msgstr "白"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "寬"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Yellow"
msgstr "黃色"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Yes"
msgstr "是"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot delete a floor when orders are still in draft for this floor."
msgstr "當仍有待處理訂單時，無法刪除樓層。"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "You cannot delete a table with orders still in draft for this table."
msgstr "當仍有待處理訂單時，無法刪除餐桌。"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/table_guests_button/table_guests_button.js:0
#, python-format
msgid "You cannot put a number that exceeds %s "
msgstr "您不能輸入一個超過%s的數字 "

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr "您不能刪除在PoS營業點中使用的樓層，請先關閉營業點:\n"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr "您無法刪除PoS營業點中使用的餐桌，請先關閉營業點."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#, python-format
msgid "at table"
msgstr "在餐桌"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "changes"
msgstr "變更"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "items"
msgstr "項目"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/order_widget/order_widget.js:0
#, python-format
msgid "or book the table for later"
msgstr "或預訂餐桌供稍後享用"
