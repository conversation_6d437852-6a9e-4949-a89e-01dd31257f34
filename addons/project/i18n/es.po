# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * project
#
# Translators:
# Wil <PERSON>, 2023
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# "<PERSON><PERSON> (lman)" <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:36+0000\n"
"PO-Revision-Date: 2025-08-07 20:26+0000\n"
"Last-Translator: \"<PERSON><PERSON> (lman)\" <<EMAIL>>\n"
"Language-Team: Spanish <https://translate.odoo.com/projects/odoo-17/project/"
"es/>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n != 0 && n % 1000000 == 0)"
" ? 1 : 2);\n"
"X-Generator: Weblate 5.12.2\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No se encontraron hitos. ¡Creemos uno!\n"
"                </p><p>\n"
"                    Lleve un seguimiento de los principales puntos de progreso a alcanzar para lograr el éxito.\n"
"                </p>\n"
"            "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
#, python-format
msgid " - Tasks by Deadline"
msgstr " - Tareas por fecha límite"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_count
msgid "# Collaborators"
msgstr "N.° de colaboradores"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "N.° de calificaciones"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr "# Tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__done_task_count
#, fuzzy
msgid "# of Done Tasks"
msgstr "N.° de tareas hechas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
#, python-format
msgid "# of Tasks"
msgstr "N.º de tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""
"#{record.milestone_count_reached.value} Hitos alcanzados de "
"#{record.milestone_count.value}"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "#{task.stage_id.name}"
msgstr "#{task.stage_id.name}"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(closed_task_count)s / %(task_count)s"
msgstr "%(closed_task_count)s / %(task_count)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"
msgstr "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s"
msgstr "%(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Burndown Chart"
msgstr "Diagrama de quemado de %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Milestones"
msgstr "Hitos de %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Rating"
msgstr "Calificación de %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Tasks Analysis"
msgstr "Análisis de las tareas de %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Updates"
msgstr "Actualizaciones de %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project_stage.py:0
#: code:addons/project/models/project_task.py:0
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(adeudado al"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(última actualización del proyecto),"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- alcanzado el"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr ""
"<b>Arrastre y suelte</b> la tarjeta para cambiar su tarea desde la etapa."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this "
"task won't be notified of the note you are logging unless you specifically "
"tag them)</i>. Use @ <b>mentions</b> to ping a colleague or # "
"<b>mentions</b> to reach an entire team."
msgstr ""
"<b>Registre notas</b> para comunicaciones internas <i>(las personas que sigan esta tarea no serán notificadas \n"
"    de la nota que está registrando a menos que las etiquete específicamente)</i>. Utilice @ <b>menciones</b> para hacer ping a un colega o # <b>menciones</b> para llegar a todo un equipo."

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br><br>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the <strong t-out=\"object.name or ''\">Planning and budget</strong> task\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey has been sent because your task has been moved to the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br>Best regards,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hola <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hola,<br><br>\n"
"            </t>\n"
"            Tómese un momento para calificar nuestros servicios relacionados a la tarea \"<strong t-out=\"object.name or ''\">Planificación y presupuesto</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                asignada a <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Cuéntenos su experiencia con nuestro servicio</strong><br>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(haga clic en una de estas caritas)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            Apreciamos sus comentarios, nos ayudan a seguir mejorando.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">Se envió esta encuesta de cliente porque su tarea se movió a la etapa <b t-out=\"object.stage_id.name or ''\">En proceso</b>.</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">Esta encuesta de cliente se envía <b t-out=\"object.project_id.rating_status_period or ''\">semanalmente</b> mientras la tarea se encuentre en la etapa <b t-out=\"object.stage_id.name or ''\">En proceso</b>.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br>Saludos,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">SuCompañía</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\">info@sucompañía.com</a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.ejemplo.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""
"<div>\n"
"    Estimado <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Tenemos el placer de comunicarle que hemos finalizado con éxito el proyecto \"<strong t-out=\"object.name or ''\">Renovaciones</strong>\".\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">Está recibiendo este correo electrónico porque su proyecto ha pasado a la fase <b t-out=\"object.stage_id.name or ''\">Hecho</b></span>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Thank you for your enquiry.<br>\n"
"    If you have any questions, please let us know.\n"
"    <br><br>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    Estimado <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Gracias por su consulta.<br>\n"
"    Si tiene alguna pregunta, póngase en contacto con nosotros.\n"
"    <br><br>\n"
"    Gracias,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr "<i class=\"fa fa-lock\"/> Privado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Cliente deshabilitado en proyectos\"/><b>"
" Calificaciones de los clientes</b> están deshabilitados en los siguientes "
"proyectos : <br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Volver al modo de edición"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "<i title=\"Private Task\" class=\"fa fa-lock\"/>"
msgstr "<i title=\"Private Task\" class=\"fa fa-lock\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr "<small class=\"text-end\">Etapa:</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Assignees</small>"
msgstr "<small class=\"text-muted\">Personas asignadas</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">Cliente</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user me-2\" aria-label=\"Empresa\" title=\"Empresa\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Done</span>"
msgstr "<span class=\"fw-normal\"> Hecho</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Tasks</span>"
msgstr "<span class=\"fw-normal\"> Tareas</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text order-2\">Closed</span>"
msgstr "<span class=\"o_stat_text order-2\">Cerrado</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text order-2\">Sub-tasks</span>"
msgstr "<span class=\"o_stat_text order-2\">Subtareas</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Collaborators\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Colaboradores\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr "<span class=\"o_stat_text\">Última calificación</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Parent Task</span>"
msgstr "<span class=\"o_stat_text\">Tarea padre</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"text-muted o_row ps-1 pb-3\">Send a rating request:</span>"
msgstr ""
"<span class=\"text-muted o_row ps-1 pb-3\">Enviar una solicitud de "
"calificación:</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">A rating request will be sent as soon as the task reaches a stage on which a Rating Email Template is defined.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Rating requests will be sent as long as the task remains in a stage on which a Rating Email Template is defined.</span>\n"
"                                                </span>"
msgstr ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">Se enviará una solicitud de calificación cuando la tarea llegue a una etapa que tenga definida una plantilla de correo para calificación.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Las solicitudes de calificación se enviarán mientras la tarea permanezca en una etapa que tenga definida una plantilla de correo para calificación.</span>\n"
"                                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Informes</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>Vista</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">Archivos adjuntos</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Fecha límite:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr "<strong>Hito:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>Proyecto:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>Hitos</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Un diccionario Python que será evaluado para proporcionar valores por "
"defecto al crear nuevos registros para este alias."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"No se puede seleccionar un colaborador más de una vez en el acceso "
"compartido del proyecto. Elimine los duplicado(s) e inténtelo de nuevo."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""
"No se puede vincular una etapa personal a un proyecto porque solo es visible"
" para su usuario correspondiente."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_private_task_has_no_parent
msgid "A private task cannot have a parent."
msgstr "Una tarea privada no puede tener una tarea padre."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_recurring_task_has_no_parent
msgid "A subtask cannot be recurrent."
msgstr "Una subtarea no puede ser recurrente."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr "Ya existe una etiqueta con el mismo nombre."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "Una tarea solo puede tener una etapa personal por usuario."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "Aceptar correos electrónicos de"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_instruction_message
msgid "Access Instruction Message"
msgstr "Mensaje de instrucciones de acceso"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_mode
msgid "Access Mode"
msgstr "Modo de acceso"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "Advertencia de acceso"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_project_stage__active
#: model:ir.model.fields,field_description:project.field_project_task__active
#: model:ir.model.fields,field_description:project.field_project_task_type__active
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "Activo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "Actividades"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_project_task_plan
#: model:ir.ui.menu,name:project.mail_activity_plan_menu_config_project
msgid "Activity Plans"
msgstr "Planes de actividad"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actvidad"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipos de actividad"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Add Milestone"
msgstr "Añadir hito"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_task_plan
msgid "Add a new plan"
msgstr "Añadir un nuevo plan"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add a note"
msgstr "Añadir una nota"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"Añada columnas para organizar sus tareas en <b>etapas</b>, <i>por ejemplo, "
"Nuevo - En proceso - Hecho</i> ."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add contacts to share the project..."
msgstr "Añada contactos para compartir el proyecto...."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Add details about this task..."
msgstr "Añada detalles sobre esta tarea..."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "Añada contenido adicional para mostrar en el correo electrónico"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Add your task once it is ready."
msgstr "Añada su tarea cuando esté lista."

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "Administrador"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Agile Scrum"
msgstr "Scrum ágil"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "Alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "Seguridad de contacto del alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain_id
msgid "Alias Domain"
msgstr "Dominio de alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias Domain Name"
msgstr "Nombre del dominio de alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_full_name
msgid "Alias Email"
msgstr "Correo electrónico del alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "Nombre del alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_status
msgid "Alias Status"
msgstr "Estado del alias"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Estado del alias evaluado en el último mensaje recibido."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "Modelo con alias"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Todos"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management_all_tasks
msgid "All Tasks"
msgstr "Todas las tareas"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr "Todos los usuarios internos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allocated_hours
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__allocated_hours
msgid "Allocated Time"
msgstr "Tiempo asignado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_allocated_hours_template
msgid "Allocated Time:"
msgstr "Tiempo asignado:"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_rating
msgid "Allow Customer Ratings"
msgstr "Permitir las calificaciones de clientes"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
#: model:ir.model.fields,field_description:project.field_project_task__analytic_account_id
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__analytic_plan_id
msgid "Analytic Plan"
msgstr "Plan analítico"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project, its tasks and its timesheets are linked. \n"
"Track the costs and revenues of your project by setting this analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"This analytic account can be changed on each task individually if necessary.\n"
"An analytic account is required in order to use timesheets."
msgstr ""
"La cuenta analítica a la que están vinculados este proyecto, sus tareas y sus partes de horas. \n"
"Lleve el seguimiento de los costes e ingresos de su proyecto al establecer esta cuenta analítica en sus documentos relacionados (por ejemplo, pedidos de venta, facturas, pedidos de compra, facturas de proveedor, gastos, etc.).\n"
"Esta cuenta analítica se puede cambiar en cada tarea de forma individual si es necesario.\n"
"Se necesita una cuenta analítica para usar partes de horas."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__analytic_account_id
msgid ""
"Analytic account to which this task and its timesheets are linked.\n"
"Track the costs and revenues of your task by setting its analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"By default, the analytic account of the project is set. However, it can be changed on each task individually if necessary."
msgstr ""
"La cuenta analítica a la que están vinculados este proyecto, sus tareas y sus partes de horas. \n"
"Lleve el seguimiento de los costes e ingresos de su proyecto al establecer esta cuenta analítica en sus documentos relacionados (por ejemplo, pedidos de venta, facturas, pedidos de compra, facturas de proveedor, gastos, etc.).\n"
"Por defecto, se establece la cuenta analítica de este proyecto. Sin embargo, puede ser cambiada de manera individual en cada tarea si fuera necesario."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "Analítica"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr ""
"Analice la rapidez con la que su equipo está terminando las tareas de su "
"proyecto y compruebe que todo avance según lo previsto."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr ""
"Analice el progreso de sus proyectos y el rendimiento de sus empleados."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__03_approved
msgid "Approved"
msgstr "Aprobado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "Archivar etapas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Archived"
msgstr "Archivado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "¿Está seguro de que desea continuar?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "¿Está seguro de que desea eliminar estas etapas?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: code:addons/project/static/src/components/subtask_one2many_field/subtask_list_renderer.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "¿Está seguro de que desea eliminar este registro?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "Flecha"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "Icono de flecha"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Assembling"
msgstr "Montaje"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Assign a responsible to your task"
msgstr "Asigne un responsable a su tarea"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr "Asignado"

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "Tareas asignadas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "Asignado a"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignees"
msgstr "Personas asignadas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignement Date"
msgstr "Fecha de asignación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "Fecha de asignación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "Fecha de asignación"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr "En riesgo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Attach all documents or links to the task directly, to have all research "
"information centralized."
msgstr ""
"Adjunte todos los documentos o enlaces directamente a la tarea con el fin de"
" tener toda la información disponible en un solo lugar."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message."
msgstr "Archivos adjuntos que no provienen de mensajes."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "Autor"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "Autogenerar tareas para actividades regulares"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_state
msgid "Automatic Kanban Status"
msgstr "Estado kanban automático"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_state
msgid ""
"Automatically modify the state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the state to 'Approved' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'Changes Requested' (orange bullet).\n"
msgstr ""
"Modifique automaticamente el estado cuando el cliente responde a los comentarios para esta etapa.\n"
" * Comentarios buenos del cliente actualizarán el estado a 'Aprobado' (viñeta verde).\n"
" * Comentarios neutros o malos establecerán el estado kanban como 'Cambios solicitados' (viñeta naranja).\n"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
#, python-format
msgid "Average Rating"
msgstr "Calificación promedio"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Calificación promedio (%)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr "Calificación promedio: Insatisfecho"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr "Calificación promedio: Bien"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr "Valoración media: Satisfecho"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr "Backlog"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "Saldo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Billed"
msgstr "Facturado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__dependent_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__dependent_ids
msgid "Block"
msgstr "Bloquear"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocked"
msgstr "Bloqueado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "Bloqueado por"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocking"
msgstr "Bloqueo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocking Tasks"
msgstr "Tareas de bloqueo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr "Lluvia de ideas"

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr "Error"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Burndown Chart"
msgstr "Diagrama de quemado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__can_be_marked_as_done
msgid "Can Be Marked As Done"
msgstr "Puede marcarse como Hecho"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_canceled
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
#, python-format
msgid "Canceled"
msgstr "Cancelado"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__02_changes_requested
#: model:mail.message.subtype,description:project.mt_task_changes_requested
#: model:mail.message.subtype,name:project.mt_project_task_changes_requested
#: model:mail.message.subtype,name:project.mt_task_changes_requested
msgid "Changes Requested"
msgstr "Cambios solicitados"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the "
"name of a customer, of a product, of a team, of a construction site, "
"etc.</i>"
msgstr ""
"Elija un <b>nombre</b> para su proyecto. <i>Puede ser lo que usted quiera: "
"el nombre de un cliente, de un producto, de un equipo, de una construcción, "
"etc.</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr ""
"Elija un <b>nombre</b> para la tarea <i>(p. ej. diseño del sitio web, compra"
" de productos...)</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Client Review"
msgstr "Valoración del cliente"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed On"
msgstr "Cerrado el"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__closed_subtask_count
msgid "Closed Sub-tasks Count"
msgstr "Número de subtareas cerradas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__closed_task_count
#: model:ir.model.fields,field_description:project.field_project_update__closed_task_count
msgid "Closed Task Count"
msgstr "Número de tareas cerradas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__closed_task_percentage
msgid "Closed Task Percentage"
msgstr "Porcentaje de tareas cerradas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed Tasks"
msgstr "Tareas cerradas"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid ""
"Collaborate efficiently with key stakeholders by sharing with them the "
"Kanban view of your tasks. Collaborators will be able to edit parts of tasks"
" and send messages."
msgstr ""
"Comparta la vista de kanban de sus tareas para colaborar eficazmente con las"
" personas involucradas. Los colaboradores podrán editar partes de las tareas"
" y enviar mensajes."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
msgid "Collaborator"
msgstr "Colaborador"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_ids
msgid "Collaborators"
msgstr "Colaboradores"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Colaboradores en proyectos compartidos"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""
"Recopile los comentarios de sus clientes mediante las solicitudes de calificación cuando una tarea pase a una etapa determinada. Para ello, defina una plantilla de correo electrónico de calificación en las etapas correspondientes.\n"
"Calificación al cambiar de etapa: se enviará automáticamente un correo electrónico cuando la tarea alcance la etapa en la que se haya establecido la plantilla de correo electrónico de calificación.\n"
"Calificación periódica: se enviará automáticamente un correo electrónico cada cierto tiempo mientras la tarea permanezca en la fase en la que está establecida la plantilla de correo electrónico de calificación."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_update__color
msgid "Color"
msgstr "Color"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr "Índice de Colores"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt."
msgstr ""
"Comuníquese con los clientes desde la tarea mediante la puerta de enlace de correo electrónico. Adjunte diseños de logos a la tarea, de modo que la información fluya desde\n"
"      los diseñadores hasta los trabajadores que imprimen la camisa."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Communication history"
msgstr "Historial de comunicación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_project_stage__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Company"
msgstr "Compañía"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "Configuración"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "Configurar etapas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "Confirmar"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#, python-format
msgid "Confirmation"
msgstr "Confirmación"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Congratulations, you are now a master of project management."
msgstr "Enhorabuena, ahora es usted un maestro de la gestión de proyectos."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr "Consultoría"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "Contacto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid "Convert Task"
msgstr "Convertir tarea"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.actions.server,name:project.action_server_convert_to_subtask
#, python-format
msgid "Convert to Task/Sub-Task"
msgstr "Convertir en tareas/subtareas "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "Redacción"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Costs"
msgstr "Costes"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "Imagen de portada"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr ""
"Cree <b>actividades</b> en las que fije tareas pendientes o programe "
"reuniones."

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "Fecha de creación"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "Crear un proyecto"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "Crear una nueva etapa en la lista de tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr "Crear proyecto"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""
"Cree proyectos para organizar sus tareas y defina un flujo de trabajo "
"diferente para cada proyecto."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr ""
"Cree proyectos para organizar sus tareas y defina un flujo de trabajo "
"diferente para cada proyecto."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "Cree tareas enviando un correo electrónico a"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr ""
"Cree tareas al enviar un correo electrónico a la dirección de correo "
"electrónico de su proyecto."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "Creado el"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "Creado el"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "Proyecto actual de la tarea"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "Etapa actual de esta tarea"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"Por ahora está disponible para todos los que puedan visualizar este "
"documento, haga clic aquí para restringirlo solo a los empleados internos."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"Por ahora está restringido solo para empleados internos, haga clic aquí para"
" hacerlo disponible a cualquiera que esté viendo el documento."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Mensaje rebotado personalizado"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Customer Email"
msgstr "Correo electrónico del cliente"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr "Opinión de los clientes"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "URL del portal de cliente"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "Calificación de clientes"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "Estado de las calificaciones del cliente"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly."
msgstr ""
"Los clientes proporcionan comentarios por correo electrónico y Odoo crea tareas automáticamente para que\n"
"      usted pueda comunicarse directamente desde la tarea."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Customers will be added to the followers of their project and tasks."
msgstr "Se añadirán los clientes como seguidores de su proyecto y tareas."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "Diariamente"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
#, python-format
msgid "Date"
msgstr "Fecha"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "Date and Stage"
msgstr "Fecha y etapa"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the state of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage/state to another."
msgstr ""
"Fecha en la que se modificó por última vez el estado de su tarea.\n"
"Gracias a esta información podrá identificar qué tareas están en pausa y obtener estadísticas sobre cuánto tiempo tarda normalmente en mover una tarea de una etapa o estado a otro."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr ""
"Fecha en la que termina este proyecto. Se toma en cuenta el calendario "
"definido en el proyecto a la hora de ver su planificación."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""
"Fecha en la que se asignó (o se eliminó la asignación) esta tarea por última"
" vez. Gracias a esta información podrá obtener estadísticas sobre cuánto "
"tarda normalmente en asignar tareas."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "Días"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "Días para la fecha límite"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
#, python-format
msgid "Deadline"
msgstr "Fecha límite"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "Estimado/a"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "Valores por defecto"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"Defina los pasos que se utilizarán en el proyecto, desde la\n"
"                creación de la tarea hasta su cierre o el de un problema.\n"
"                Estas etapas se utilizarán para llevar seguimiento del progreso al \n"
"                resolver una tarea o un problema."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr ""
"Defina los pasos a seguir en sus proyectos desde la creación hasta la "
"finalización."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr ""
"Defina los pasos a seguir en sus tareas desde la creación hasta la "
"finalización."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/delete_subtasks_confirmation_dialog/delete_subtasks_confirmation_dialog.js:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_project_stage_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
#, python-format
msgid "Delete Milestone"
msgstr "Eliminar hito"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#, python-format
msgid "Delete Project Stage"
msgstr "Eliminar etapa del proyecto"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
#, python-format
msgid "Delete Stage"
msgstr "Eliminar etapa"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/delete_subtasks_confirmation_dialog/delete_subtasks_confirmation_dialog.js:0
#, python-format
msgid ""
"Deleting a task will also delete its associated sub-tasks. If you wish to "
"preserve the sub-tasks, make sure to unlink them from their parent task "
"beforehand. Are you sure you want to proceed?"
msgstr ""
"Si elimine esta tarea también eliminará las subtareas. Si desea preservar "
"las subtareas, asegúrese de desvincularlas de la tarea padre antes. ¿Está "
"seguro de que quiere proceder?"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""
"Ofrezca sus servicios automáticamente cada vez que se alcance un hito, se "
"puede hacer vinculándolo a un elemento del pedido de venta."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr "Entregado"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__dependent_tasks_count
#, python-format
msgid "Dependent Tasks"
msgstr "Tareas dependientes"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model:ir.model.fields,field_description:project.field_report_project_task_user__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "Descripción"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr ""
"Texto de descripción para proporcionar más información y contexto sobre este"
" proyecto"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr "Diseño"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "Determine el orden de ejecución de las tareas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr "Desarrollo"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "Resumen"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr "Marketing digital"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__disabled_rating_warning
msgid "Disabled Rating Warning"
msgstr "Advertencia de clasificación deshabilitada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "Descartar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_access_mode
msgid "Display Access Mode"
msgstr "Modo de acceso a la pantalla"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_in_project
msgid "Display In Project"
msgstr "Mostrar en el proyecto"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_parent_task_button
msgid "Display Parent Task Button"
msgstr "Mostrar el botón de tarea padre"

#. module: project
#. odoo-python
#: code:addons/project/models/digest_digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"No tiene acceso, omita esta información para el resumen del correo "
"electrónico del usuario"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__done
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_update__status__done
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_done
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "Hecho"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr "Borrador"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid ""
"Each user should have at least one personal stage. Create a new stage to "
"which the tasks can be transferred after the selected ones are deleted."
msgstr ""
"Cada usuario debería tener al menos una etapa persona. Cree una nueva etapa "
"a las que se puedan transferir las tareas después de eliminar la "
"seleccionada."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__edit
msgid "Edit"
msgstr "Editar"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr "Editando"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_email
msgid "Email"
msgstr "Correo electrónico"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_email
msgid "Email Alias"
msgstr "Alias de correo electrónico"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "Plantilla de correo electrónico"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr ""
"Correos electrónicos que estaban marcados como CC en los correos recibidos "
"de esta tarea y no están vinculados a un cliente existente."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "CC del correo electrónico"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""
"Dominio del correo electrónico, p. ej. \"ejemplo.com\" en "
"\"<EMAIL>\""

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr "Correos electrónicos enviados a"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Employees Only"
msgstr "Solo empleados"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "Fecha de finalización"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "Fecha de fin"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "Error. No se puede crear una jerarquía recursiva de tareas."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr "Todos pueden proponer ideas y el editor marcará las mejores como"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Expected"
msgstr "Previsto"

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr "Experimento"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "Fecha de expiración"

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr "Externo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "Información adicional"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "Favorito"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Favorite Projects"
msgstr "Proyectos favoritos"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr "Documento final"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "Plegado en kanban"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow and comments tasks of your projects"
msgstr "Siga y comente las tareas de sus proyectos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow the evolution of your projects"
msgstr "Siga la evolución de sus proyectos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr ""
"Siga este proyecto para seguir automáticamente los eventos asociados a sus "
"tareas y problemas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Followed"
msgstr "Seguido"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr "Actualizaciones seguidas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome p. ej. fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "Para siempre"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Frequency"
msgstr "Frecuencia"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr ""
"Obtenga un panorama del estado de su proyecto y comparta su progreso con las"
" partes interesadas fundamentales."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback and evaluate the performance of your employees"
msgstr ""
"Reciba retroalimentación de sus clientes y evalúe el rendimiento de sus "
"empleados."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Grant Portal Access"
msgstr "Otorgar acceso al portal "

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
" Employees automatically get access to the tasks they are assigned to."
msgstr ""
"Conceda a los empleados acceso a su proyecto o tareas añadiéndolos como "
"seguidores. Los empleados obtienen automáticamente acceso a las tareas a las"
" que están asignados."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Grant portal users access to your project or tasks by adding them as "
"followers. Customers automatically get access to their tasks in their "
"portal."
msgstr ""
"Conceda a los usuarios del portal acceso a su proyecto o tareas  "
"añadiéndolos como seguidores. Los clientes obtienen automáticamente acceso a"
" sus tareas en su portal."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Group By"
msgstr "Agrupar por"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks."
msgstr ""
"Gestione la recopilación de ideas dentro de las tareas de su nuevo proyecto "
"y hable acerca de ellas en el chatter de tareas."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Handoff"
msgstr "Transferencia"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "Cara feliz"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__has_late_and_unreached_milestone
msgid "Has Late And Unreached Milestone"
msgstr "Tiene un hito atrasado y no alcanzado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "Alta"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "Historial"

#. module: project
#: model:project.project,name:project.project_project_4
msgid "Home Make Over"
msgstr "Renovación de la casa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "Horas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "¿Cómo va este proyecto?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID del registro padre que tiene el alias. (ejemplo: el proyecto que contiene"
" el alias para la creación de tareas)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "Ideas"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""
"Si esta opción está activada, esta etapa se mostrará como plegada en la "
"vista kanban de sus proyectos. Los proyectos en una etapa plegada se "
"consideran cerrados."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""
"Si esta opción está activada, se enviará automáticamente una solicitud de calificación por correo electrónico al cliente cuando la tarea llegue a esta etapa.\n"
"Otra opción es que se envíe de manera periódica durante el tiempo que la tarea permanezca en esa etapa, según la configuración de su proyecto.\n"
"Para utilizar esta función, asegúrese de que la opción \"Calificaciones de los clientes\" está activada en su proyecto."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr ""
"Si esta opción está activada, se enviará automáticamente un correo "
"electrónico al cliente cuando el proyecto llegue a esta etapa."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr ""
"Si esta opción está activada, se enviará automáticamente un correo "
"electrónico al cliente cuando la tarea llegue a esta etapa."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Si se configura, este contenido se enviará automáticamente a usuarios no "
"autorizados en lugar del mensaje por defecto."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""
"Si el campo activo se desmarca, permite ocultar el proyecto sin eliminarlo."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__01_in_progress
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,name:project.project_stage_1
#, python-format
msgid "In Progress"
msgstr "En proceso"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr "En desarrollo"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
#, python-format
msgid "Inbox"
msgstr "Bandeja de entrada"

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "Interno"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal Note"
msgstr "Nota interna"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"Correo electrónico interno asociado con este proyecto. Los mensajes "
"entrantes se sincronizan automáticamente con las Tareas (u opcionalmente con"
" las incidencias si el módulo de seguimiento de incidencias está instalado)."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal notes are only displayed to internal users."
msgstr "Las notas internas sólo se muestran a los usuarios internos."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Invalid operator: %s"
msgstr "Operador no válido: %s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Invalid value: %s"
msgstr "Valor no válido: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Invite Collaborators"
msgstr "Invitar colaboradores"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Invite People"
msgstr "Invitar gente"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users (private)"
msgstr "Usuarios internos invitados (privado)"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr "Usuarios del portal invitados y todos los usuarios internos (público)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Invoiced"
msgstr "Facturado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_exceeded
msgid "Is Deadline Exceeded"
msgstr "¿Se ha superado la fecha límite?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_future
msgid "Is Deadline Future"
msgstr "La fecha límite está en el futuro"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
#: model:ir.model.fields,field_description:project.field_report_project_task_user__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_milestone_exceeded
msgid "Is Milestone Exceeded"
msgstr "¿Ha alcanzado su hito?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
#, python-format
msgid "Is toggle mode"
msgstr "Está en modo alternancia"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "Versión del problema"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON que mapea IDs de un campo many2one a segundos utilizados"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Lleve seguimiento del progreso de sus tareas desde su creación hasta su finalización.<br>\n"
"                    Chatee en tiempo real o por correo electrónico para colaborar de forma eficiente."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Lleve seguimiento del progreso de sus tareas desde su creación hasta su finalización.<br>\n"
"                Chatee en tiempo real o por correo electrónico para colaborar de forma eficiente."

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr "Valor de la tarea abierta del proyecto (KPI)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "Mes anterior"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Last Stage Update"
msgstr "Última actualización de la etapa"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_id
msgid "Last Update"
msgstr "Última actualización"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_color
msgid "Last Update Color"
msgstr "Última actualización de color"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_status
msgid "Last Update Status"
msgstr "Último estado de actualización"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "Última actualización el"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "Actividades retrasadas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Late Milestones"
msgstr "Hitos atrasados"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
#, python-format
msgid "Later"
msgstr "Más tarde"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Leave a comment"
msgstr "Escriba un comentario"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>project</b>."
msgstr "Creemos su primer <b>proyecto</b>."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>stage</b>."
msgstr "Creemos su primera <b>etapa</b>."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "Creemos su primera <b>tarea</b>."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your second <b>stage</b>."
msgstr "Creemos su segunda <b>etapa</b>."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"Regresemos a su <b>vista kanban</b> para tener una vista general de sus "
"próximas tareas."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's start working on your task."
msgstr "Empecemos a trabajar en su tarea."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "Esperemos a que sus clientes se manifiesten."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Link"
msgstr "Enlace"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr "En vivo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Detección de entradas basada en partes locales"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr "Diseño de logotipo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Look for the"
msgstr "Busque el icono"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "Baja"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr "Archivos adjuntos principales"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""
"Administre el ciclo de vida de su proyecto usando la vista kanban. Añada proyectos recién adquiridos,\n"
"      asígnelos y use"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Manufacturing"
msgstr "Fabricación"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Margin"
msgstr "Margen"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#, python-format
msgid "Mark as done"
msgstr "Marcar como hecho"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Material Sourcing"
msgstr "Abastecimiento de material"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr ""
"Evalúe la satisfacción de sus clientes enviando solicitudes de calificación "
"cuando sus tareas alcancen una determinada fase."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr "Miembros"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "Menú"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "Mensaje"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_project__milestone_ids
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Milestone"
msgstr "Hito"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count
msgid "Milestone Count"
msgstr "Números de hitos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count_reached
msgid "Milestone Count Reached"
msgstr "Número de hitos alcanzados"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_project_task__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Milestones"
msgstr "Hitos"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Mixing"
msgstr "Mezcla"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "Meses"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "My Deadline"
msgstr "Mi fecha límite"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "Mis favoritos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Projects"
msgstr "Mis proyectos"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_my_task
#: model:ir.ui.menu,name:project.menu_project_management_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "My Tasks"
msgstr "Mis tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr "Mis actualizaciones"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Name"
msgstr "Nombre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__name_cropped
msgid "Name Cropped"
msgstr "Nombre recortado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the Tasks"
msgstr "Nombre de las tareas"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr ""
"Nombre utilizado para referirse a las tareas de su proyecto, por ejemplo, "
"tareas, tickets, sprints, etc."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "Cara neutral"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr "Nueva función"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#, python-format
msgid "New Milestone"
msgstr "Nuevo hito"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr "Nuevos pedidos"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_project_calendar/project_project_calendar_controller.js:0
#, python-format
msgid "New Project"
msgstr "Nuevo proyecto"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr "Nuevos proyectos"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Request"
msgstr "Nueva solicitud"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
#, python-format
msgid "New Task"
msgstr "Nueva tarea"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Más reciente"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Next"
msgstr "Siguiente"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Next Activity"
msgstr "Actividad siguiente"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr "Sin cliente"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr "Sin hito"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Project"
msgstr "Sin proyecto"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "No Subject"
msgstr "Sin asunto"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr "No se encontraron tipos de actividad, ¡creemos uno!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid "No collaborators found"
msgstr "No se encontraron colaboradores"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "Todavía no hay calificaciones de los clientes"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "¡Todavía no hay información!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "No se encontraron proyectos. ¡Creemos uno!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "No se encontraron etapas. ¡Creemos una!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "No se encontraron etiquetas. ¡Creemos una!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr "No se encontraron tareas. ¡Creemos una!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "No se encontraron actualizaciones. ¡Creemos una!"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Ninguno"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Not Implemented."
msgstr "No se implementó."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "Nota"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__projects_count
msgid "Number of Projects"
msgstr "Número de proyectos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__tasks_count
msgid "Number of Tasks"
msgstr "Número de tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr "Número de documentos adjuntos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr "Atrasado"

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "Diseño de la oficina"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Old Completed Sprint"
msgstr "Sprint completado antiguo"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr "En espera"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr "A tiempo"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "Una vez al mes"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Only jpeg, png, bmp and tiff images are allowed as attachments."
msgstr ""
"Sólo se permiten imágenes jpeg, png, bmp y tiff como archivos adjuntos."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "¡Uy! Ocurrió un error. Intente actualizar la página e inicie sesión."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__open_task_count
msgid "Open Task Count"
msgstr "Número de tareas abiertas"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Open Tasks"
msgstr "Tareas abiertas"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operación no admitida"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID opcional de un hilo (registro) al que se adjuntarán todos los mensajes "
"entrantes, incluso si no fueron respuestas del mismo. Si se establece, se "
"deshabilitará completamente la creación de nuevos registros."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Organize priorities amongst orders using the"
msgstr "Organice las prioridades entre los pedidos con"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Organice sus tareas distribuyéndolas en su flujo de trabajo.<br>\n"
"                    Colabore eficazmente chateando en tiempo real o por correo electrónico."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr "Otros"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "Tareas sobrepasadas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr "Ideas para páginas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "Modelo padre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID del hilo del registro padre"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
#, python-format
msgid "Parent Task"
msgstr "Tarea padre"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modelo padre que contiene el alias. El modelo que contiene la referencia del"
" alias no es necesariamente el modelo dado por alias_model_id (example: "
"project (parent_model) and task (model))"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
#, python-format
msgid ""
"Partner company cannot be different from its assigned projects' company"
msgstr ""
"La empresa del contacto no puede ser diferente a la empresa de sus proyectos"
" asignados."

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
#, python-format
msgid "Partner company cannot be different from its assigned tasks' company"
msgstr ""
"La empresa del contacto no puede ser diferente a la empresa de sus tareas "
"asignadas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"People invited to collaborate on the project will have portal access rights."
msgstr ""
"Las personas invitadas a colaborar en un proyecto tendrán permiso de acceso "
"al portal."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"Las personas que podrán ver este proyecto y sus tareas.\n"
"\n"
"- Usuarios internos invitados: al seguir un proyecto, los usuarios internos tendrán acceso a todas las tareas. De lo contrario, solo tendrán acceso a las tareas específicas que están siguiendo. Un usuario con permiso de acceso de administrador en el proyecto aún puede acceder al proyecto y a sus tareas, incluso si no es parte de los seguidores.\n"
"\n"
"- Todos los usuarios internos: todos los usuarios internos pueden acceder al proyecto y a todas sus tareas.\n"
"\n"
"- Usuarios del portal invitados y todos los usuarios internos: todos los usuarios internos pueden acceder al proyecto y a todas sus tareas. Al seguir un proyecto, los usuarios del portal solo tendrán acceso a las tareas específicas que estén siguiendo.\n"
"\n"
"Al compartir un proyecto en modo de solo lectura, el usuario del portal será redireccionado a su portal y podrá ver las tareas que está siguiendo, pero no editarlas. Al compartir un proyecto en modo de edición, el usuario del portal será redireccionado a las vistas de kanban y de lista de las tareas. Además, podrá modificar un número determinado de campos en las tareas.\n"
"\n"
"En cualquier caso, un usuario interno sin permisos de acceso al proyecto aún puede acceder a una tarea, siempre y cuando cuente con la URL correspondiente (y que forme parte de los seguidores si el proyecto es privado)."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Porcentaje de calificaciones positivas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Personal Stage"
msgstr "Etapa personal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "Estado de la etapa personal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_ids
msgid "Personal Stages"
msgstr "Etapas personales"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "Etapa de la tarea personal"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Planned Date"
msgstr "Fecha planificada"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"Por favor, elimine las tareas al proyecto vinculadas a las cuentas que "
"quiere eliminar."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Podcast and Video Production"
msgstr "Producción de podcasts y vídeos"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Política para enviar un mensaje en el documento usando la pasarela de correo.\n"
"- Todos: cualquiera puede enviar\n"
"- Contactos: sólo contactos autenticados\n"
"- Seguidores: sólo seguidores del documento relacionado o miembros de los siguientes canales\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "URL de acceso al portal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__portal_user_names
msgid "Portal User Names"
msgstr "Nombres de usuarios del portal"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr ""
"Se eliminaran a los usuarios del portal como seguidores del proyecto y sus "
"tareas."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Previous"
msgstr "Anterior"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Prioritize your tasks by marking important ones using the"
msgstr "Marque las tareas más importantes con"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#, python-format
msgid "Priority"
msgstr "Prioridad"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr "Prioridad: {{'Importante' if task.priority == '1' else 'Normal'}}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility_warning
msgid "Privacy Visibility Warning"
msgstr "Advertencia de visibilidad de privacidad"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.js:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_model.js:0
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
#, python-format
msgid "Private"
msgstr "Privado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Private Tasks"
msgstr "Tareas privadas"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"Private tasks cannot be converted into sub-tasks. Please set a project for "
"the task to gain access to this feature."
msgstr ""
"Las tareas privadas no se pueden convertir en subtareas. Seleccione un "
"proyecto para la tarea para tener acceso a la funcionalidad."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Profitability"
msgstr "Rentabilidad"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "Progreso"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress_percentage
msgid "Progress Percentage"
msgstr "Porcentaje de progreso"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_project_update__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
#, python-format
msgid "Project"
msgstr "Proyecto"

#. module: project
#: model:ir.actions.act_window,name:project.project_collaborator_action
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Project Collaborators"
msgstr "Colaboradores del proyecto"

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr "Número de proyectos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "Gerente de proyecto"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "Hito de proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "Nombre del proyecto"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr "Estado de la calificación del proyecto"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__project_id
msgid "Project Shared"
msgstr "Proyecto compartido"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "Compartir proyectos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "Compartir proyectos: tarea"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "Etapa del proyecto"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "Cambio de etapa del proyecto"

#. module: project
#: model:ir.model,name:project.model_project_project_stage_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "Asistente para eliminar la etapa del proyecto"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "Etapas del proyecto"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "Etiquetas del proyecto"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Task Stage Delete Wizard"
msgstr "Asistente para eliminar la etapa de las tareas del proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "Tareas del proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "Project Title"
msgstr "Título del proyecto"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "Actualización del proyecto"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Project Updates"
msgstr "Actualizaciones del proyecto"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "Visibilidad del proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "Descripción del proyecto..."

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr "Estado del proyecto - {{ object.name }}"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "Tareas del proyecto"

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "Proyecto: proyecto terminado"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr "Proyecto: solicitud de confirmación"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
msgid "Project: Send rating"
msgstr "Proyecto: Enviar calificación"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr "Proyecto: Solicitud de calificación de la tarea"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_tags__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__project_ids
#: model:ir.model.fields,field_description:project.field_res_partner__project_ids
#: model:ir.model.fields,field_description:project.field_res_users__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#, python-format
msgid "Projects"
msgstr "Proyectos"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr ""
"Los proyectos incluyen las tareas correspondientes a un mismo tema, y cada "
"uno cuenta con su propio tablero."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""
"Proyectos en los que está presente esta etapa. Si sigue un flujo de trabajo "
"similar en varios proyectos, puede compartir esta etapa entre ellos y así "
"obtener información consolidada."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Properties"
msgstr "Propiedades"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Published"
msgstr "Publicado"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Published on"
msgstr "Publicado el"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Publishing"
msgstr "Edición"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "Trimestralmente"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Quickly check the status of tasks for approvals or change requests and "
"identify those on hold until dependencies are resolved with the hourglass "
"icon."
msgstr ""
"Revise el estado de las tareas para aprobarlas o cambiar sus solicitudes. "
"Con el icono de reloj de arena puede identificar las que están en pausa "
"hasta que se resuelvan las dependencias."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "Calificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr "Calificación (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "Texto de calificación media"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "Plantilla del correo de calificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "Frecuencia de calificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Última retroalimentación de la calificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "Última imagen de la calificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "Último valor de la calificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr "Fecha límite de solicitud de calificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Satisfacción de calificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "Texto de calificación"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#, python-format
msgid "Rating Value (/5)"
msgstr "Valor de calificación (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "Número de calificaciones"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_milestone__rating_ids
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model:ir.model.fields,field_description:project.field_project_update__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "Alcanzado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__reached_date
msgid "Reached Date"
msgstr "Fecha alcanzada"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__read
msgid "Readonly"
msgstr "Sólo lectura"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "Recepción de {{ object.name }}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "Destinatarios"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID del hilo de registro"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Recording"
msgstr "Registrando"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "Recurrencia"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "Recurrente"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurring Tasks"
msgstr "Tareas recurrentes"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr "Rechazado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "Documento relacionado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "ID del documento relacionado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "Modelo de documento relacionado"

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "Renovaciones"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "Repetir cada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_unit
msgid "Repeat Unit"
msgstr "Unidad de repetición"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "Informes"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr "Investigación"

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "Investigación y desarrollo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr "Proyecto de investigación"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr "Investigando"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr "Asignación de recursos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Revenues"
msgstr "Ingresos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "Cara triste"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
#, python-format
msgid "Save the task to be able to drag images in description"
msgstr "Guardar la tarea para poder arrastrar imágenes en la descripción"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
#, python-format
msgid "Save the task to be able to paste images in description"
msgstr "Guardar la tarea para poder pegar imágenes en la descripción"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Schedule your activity once it is ready."
msgstr "Programe su actividad cuando esté lista."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Script"
msgstr "Guion"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr "Buscar <span class=\"nolabel\"> (en Contenido)</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "Buscar proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "Buscar actualización"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Buscar en todos"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Assignees"
msgstr "Buscar en personas asignadas"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Customer"
msgstr "Buscar en clientes"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr "Buscar en mensajes"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Milestone"
msgstr "Buscar en hito"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Priority"
msgstr "Buscar en prioridad"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "Buscar en Proyecto"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Ref"
msgstr "Buscar en referencias"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr "Buscar en etapas"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Status"
msgstr "Buscar en estado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "Token de seguridad"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#, python-format
msgid "Send"
msgstr "Enviar"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_project_stage__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "Establecer imagen de portada"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
#, python-format
msgid "Set Status"
msgstr "Establecer estado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr ""
"Establezca una plantilla de correo electrónico de calificación en las etapas"

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr ""
"Establezca en las etapas del proyecto para indicar a los clientes cada que "
"un proyecto llegue a esta etapa"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_priority_switch_field/project_task_priority_switch_field.js:0
#, python-format
msgid "Set priority as %s"
msgstr "Establecer prioridad como %s"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
#, python-format
msgid "Set state as..."
msgstr "Establezca estado como..."

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr ""
"Establezca esta plantilla en una etapa de proyecto para solicitar "
"retroalimentación a sus clientes. Habilite la función \"calificación de "
"clientes\" en el proyecto"

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"Establezca esta plantilla en una etapa de proyecto para automatizar el "
"correo electrónico cuando las tareas pasen a las etapas"

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "Ajustes"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr "Compartir"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Editable"
msgstr "Compartir con formato editable"

#. module: project
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Share Project"
msgstr "Compartir proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Read-only"
msgstr "Compartir con formato de solo lectura"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on Dashboard"
msgstr "Mostrar proyecto en el tablero"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "Desde"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr "Desarrollo de Software"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Sorry. You can't set a task as its parent task."
msgstr "Lo siento. No puede establecer una tarea como tarea padre."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr "Especificaciones"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Backlog"
msgstr "Backlog de sprint"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Complete"
msgstr "Sprint completo"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint in Progress"
msgstr "Sprint en proceso"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Stage"
msgstr "Etapa"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "Etapa cambiada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "Propietario de la etapa"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "Etapa cambiada"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Stage: %s"
msgstr "Etapa: %s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__stages_active
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stages_active
msgid "Stages Active"
msgstr "Etapas activas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__stage_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "Etapas para eliminar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Starred Tasks"
msgstr "Tareas favoritas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__state
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__state
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
msgid "State"
msgstr "Estado"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_update__status
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Status"
msgstr "Estado"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Status Update - "
msgstr "Actualización de estado -"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya ha pasado\n"
"Hoy: la fecha límite es hoy\n"
"Planificada: actividades futuras."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__duration_tracking
msgid "Status time"
msgstr "Tiempo del estado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task Count"
msgstr "Número de subtareas"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "Subtareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_allocated_hours
msgid "Sub-tasks Allocated Time"
msgstr "Tiempo asignado a las subtareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr "Enviado el"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_allocated_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr ""
"Suma de las horas asignadas para todas las subtareas (y sus propias "
"subtareas) vinculadas a esta tarea. Normalmente es menor o igual que las "
"horas asignadas a esta tarea."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "Resumen"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr "Impresión de camisetas"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tags"
msgstr "Etiquetas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__task_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Task"
msgstr "Tarea"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "Actividades de las tareas"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_approved
#: model:mail.message.subtype,name:project.mt_task_approved
msgid "Task Approved"
msgstr "Tarea aprobada"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_canceled
#: model:mail.message.subtype,name:project.mt_task_canceled
msgid "Task Canceled"
msgstr "Tarea cancelada "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count
#: model:ir.model.fields,field_description:project.field_project_update__task_count
msgid "Task Count"
msgstr "Número de tareas"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "Tarea creada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_project_task__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "Dependencias de tareas"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_done
#: model:mail.message.subtype,name:project.mt_task_done
msgid "Task Done"
msgstr "Tarea hecha"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_in_progress
#: model:mail.message.subtype,name:project.mt_project_task_in_progress
#: model:mail.message.subtype,name:project.mt_task_in_progress
msgid "Task In Progress"
msgstr "Tarea en proceso"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "Registros de tareas"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_task_plan
msgid "Task Plans"
msgstr "Planes de tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr "Propiedades de la tarea"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "Calificación de tarea"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Recurrencia de tareas"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "Etapa de la tarea"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "Etapa de tarea cambiada"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "Etapas de la tarea"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "Título de tarea"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "Título de tarea..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_waiting
#: model:mail.message.subtype,name:project.mt_project_task_waiting
#: model:mail.message.subtype,name:project.mt_task_waiting
msgid "Task Waiting"
msgstr "Tarea pendiente"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_approved
msgid "Task approved"
msgstr "Tarea aprobada"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_canceled
msgid "Task canceled"
msgstr "Tarea cancelada"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_done
msgid "Task done"
msgstr "Tarea hecha"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "Tarea:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task: Rating Request"
msgstr "Tareas: solicitud de calificación"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_project_milestone__task_ids
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_project_tags__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model:ir.ui.menu,name:project.menu_project_management
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model:project.project,label_tasks:project.project_project_4
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Tasks"
msgstr "Tareas"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "Análisis de tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "Gestión de tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "Etapas de tareas"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
#, python-format
msgid "Tasks in Recurrence"
msgstr "Tareas en recurrencia"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr "Pruebas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "The Burndown Chart must be grouped by"
msgstr "El gráfico de trabajo pendiente debe estar agrupado por"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "La etapa personal del usuario actual."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "La etapa de la tarea personal del usuario actual."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr ""
"El documento no existe o no cuenta con los permisos de acceso necesarios."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The end date should be in the future"
msgstr "La fecha de finalización debe ser en el futuro"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "Se ha añadido el siguiente hito:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "Se han añadido los siguientes hitos:"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The interval should be greater than 0"
msgstr "El intervalo debe ser mayor que 0"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"El modelo (Tipo de documento de Odoo) al que corresponde este alias. "
"Cualquier correo entrante que no sea respuesta a un registro existente, "
"causará la creación de un nuevo registro de este modelo (por ejemplo, una "
"tarea de proyecto)."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nombre de este alias de correo electrónico. Por ejemplo, \"trabajos\",  "
"si lo que quiere es obtener los correos para <<EMAIL>>"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project and the associated partner must be linked to the same company."
msgstr ""
"El proyecto y el contacto asociado deben estar vinculados a la misma "
"empresa."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""
"No se puede compartir el proyecto con los destinatarios debido a su "
"privacidad. Defina la privacidad como \"Visible para clientes que siguen\" "
"para que el destinatario tenga acceso."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project's company cannot be changed if its analytic account has analytic"
" lines or if more than one project is linked to it."
msgstr ""
"No puede cambiar la empresa del proyecto si su cuenta analítica tiene líneas"
" analíticas o si tiene más de un proyecto vinculado."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr ""
"La fecha de inicio del proyecto debe ser anterior a su fecha de "
"finalización."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "The search does not support the %s operator or %s value."
msgstr "La búsqueda no admite el operador %s o el valor %s."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"The task and the associated partner must be linked to the same company."
msgstr ""
"La tarea y el contacto asociado deben estar vinculados a la misma empresa."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""
"No se puede compartir la tarea con los destinatarios debido a su privacidad."
" Defina la privacidad como \"Visible para clientes que siguen\" para que el "
"destinatario tenga acceso."

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "The view must be grouped by date and by stage_id"
msgstr "La vista debe estar agrupada por fecha y por stage_id"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "There are no comments for now."
msgstr "No hay comentarios por ahora."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "No hay proyectos."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "No hay calificaciones para este proyecto en este momento."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "No hay tareas."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "There is nothing to report."
msgstr "No hay nada que comunicar."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"They can edit shared project tasks and view specific documents in read-only "
"mode on your website. This includes leads/opportunities, quotations/sales "
"orders, purchase orders, invoices and bills, timesheets, and tickets."
msgstr ""
"Pueden editar tareas compartidas de proyectos y ver documentos específicos "
"en modo de solo lectura desde su sitio web. Esto incluye leads y "
"oportunidades, presupuestos y pedidos de venta, pedidos de compra, facturas,"
" partes de horas y tickets."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
#, python-format
msgid "This Month"
msgstr "Este mes"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#, python-format
msgid "This Week"
msgstr "Esta semana"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid ""
"This is a preview of how the project will look when it's shared with "
"customers and they have editing access."
msgstr ""
"Esta es una vista previa de cómo se verá el proyecto cuando se comparta con "
"los clientes y tengan acceso de edición."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"This project is associated with %s, whereas the selected stage belongs to "
"%s. There are a couple of options to consider: either remove the company "
"designation from the project or from the stage. Alternatively, you can "
"update the company information for these records to align them under the "
"same company."
msgstr ""
"Este proyecto está asociado con %s, mientras que la etapa seleccionada "
"pertenece a %s. Hay opciones que debe considerar, por ejemplo, puede quitar "
"la designación de la empresa del proyecto o de la etapa. Además, puede "
"actualizar la información de la empresa para estos registros para que "
"funcionen en la misma empresa."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"This project is not associated to any company, while the stage is associated"
" to %s. There are a couple of options to consider: either change the "
"project's company to align with the stage's company or remove the company "
"designation from the stage"
msgstr ""
"Este proyecto no está asociado a ninguna empresa, aunque la etapa está "
"asociada a %s. Hay un par de opciones que considerar: ya sea cambiar el "
"proyecto de empresa para que funcionen en la misma empresa, o eliminar la "
"designación de la empresa de la etapa."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#, python-format
msgid "This task is blocked by another unfinished task"
msgstr "Esta tarea está bloqueada por otra tarea no terminada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""
"Esto archivará las etapas y todas las tareas que contienen de los siguientes"
" proyectos:"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"Representan las diferentes categorías de cosas que se deben hacer (p. ej. "
"\"Llamar\" o \"Enviar correo electrónico\")."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "Gestión del tiempo"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr "Consejo: cree tareas desde sus correos electrónicos"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Use task states to keep track of your tasks' progression"
msgstr ""
"Consejo: use los estados en las tareas para llevar seguimiento del avance de"
" sus tareas"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#, python-format
msgid "Title"
msgstr "Título"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Bill"
msgstr "A facturar"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "Pendiente"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Invoice"
msgstr "A facturar"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr "A imprimir"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"Use actividades y estados en las tareas que le ayudarán a organizarse con eficacia.<br>\n"
"                 Comuníquese en tiempo real o por correo electrónico para colaborar de manera eficiente."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid ""
"To transform a task into a sub-task, select a parent task. Alternatively, "
"leave the parent task field blank to convert a sub-task into a standalone "
"task."
msgstr ""
"Para transformar una tarea en una subtarea, seleccione la tarea padre. "
"También puede dejar el campo de la tarea padre en blanco para que se "
"convierta en una tarea."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#, python-format
msgid "Today"
msgstr "Hoy"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "Siga la satisfacción de los clientes en tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr ""
"Haga un seguimiento de los principales puntos de progreso que se deben "
"alcanzar para lograr el éxito."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Track major progress points that must be reached to achieve success."
msgstr ""
"Haga un seguimiento de los principales puntos de progreso que se deben "
"alcanzar para lograr el éxito."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid ""
"Track project costs, revenues, and margin by setting the analytic account "
"associated with the project on relevant documents."
msgstr ""
"Realice un seguimiento de los costes, ingresos y márgenes del proyecto "
"estableciendo la cuenta analítica asociada al proyecto en los documentos "
"pertinentes."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Track the profitability of your project with analytic accounting. Select the"
" analytic plan for new projects:"
msgstr ""
"Realice un seguimiento de las ganancias de su proyecto con contabilidad "
"analítica. Seleccione el plan analítico para nuevos proyectos:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Track the profitability of your projects. Any project, its tasks and "
"timesheets are linked to an analytic account and any analytic account "
"belongs to a plan."
msgstr ""
"Lleve seguimiento de la rentabilidad de sus proyectos. Cualquier proyecto, "
"sus tareas y partes de horas están vinculados a una cuenta analítica y todas"
" las cuentas analíticas pertenecen a un plan."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "Lleve el seguimiento del progreso de sus proyectos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "Lleve seguimiento del tiempo dedicado a proyectos y tareas."

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr ""
"No se pueden ver las etiquetas transparentes desde la vista kanban de sus "
"proyectos y tareas."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "Dos veces al mes"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Two tasks cannot depend on each other."
msgstr "Dos tareas no pueden depender la una de la otra."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción en el registro."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#, python-format
msgid "Unarchive Projects"
msgstr "Desarchivar proyectos"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid "Unarchive Tasks"
msgstr "Desarchivar tareas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Unassigned"
msgstr "No asignado"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr "Cuenta analítica desconocida"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
msgid "Until"
msgstr "Hasta"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__update_ids
msgid "Update"
msgstr "Actualización"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr "Actualización realizada"

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr "Uso"

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr "Usar hitos"

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "Usar calificación en el proyecto"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "Usar tareas recurrentes"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "Use etapas en el proyecto"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "Use dependencias de tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "Usar tareas como"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Use This For My Project"
msgstr "Usar esto para mi proyecto"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "Use etiquetas para categorizar sus tareas."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Use the"
msgstr "Use"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"main changes about this task."
msgstr ""
"Utilice el chatter para <b>enviar correos electrónicos</b> y comunicarse "
"eficazmente con sus clientes. Añada nuevas personas en la lista de "
"seguidores para que conozcan los principales cambios sobre esta tarea."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""
"Utilice estas palabras clave en el título para establecer nuevas tareas:\n"
"\n"
"        30h Asignar 30 horas a la tarea\n"
"        #etiquetas Asignar etiquetas a la tarea\n"
"        @usuario Asignar la tarea a un usuario\n"
"        ! Asignar prioridad alta a la tarea\n"
"\n"
"        Asegúrese de utilizar el formato y orden correcto. Por ejemplo: Mejorar la pantalla de configuración 5h #función #v16 @Mitchell!"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__user_id
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "Usuario"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "Vista"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "View Task"
msgstr "Ver tarea"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "View Tasks"
msgstr "Ver tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "Visibilidad"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Visible"
msgstr "Visible"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__04_waiting_normal
#, python-format
msgid "Waiting"
msgstr "En espera"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr ""
"Busque una mejor forma de <b>gestionar sus proyectos</b>? <i>Aquí "
"empieza.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr "Rediseño del sitio web"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "Semanalmente"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "Semanas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"When sharing a project in edit mode, you are giving people access to the kanban and list views of your tasks.\n"
"                    Collaborators will be able to view and modify part of the tasks' information."
msgstr ""
"Al compartir un proyecto en modo edición también puede dar acceso a la vista de lista y kanban de las tareas.\n"
"                    Los colaboradores podrán ver y modificar parte de la información de la tarea."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"When sharing a project in read-only mode, you are giving people access to the tasks in their portal.\n"
"                    These people will be able to view the tasks, but not edit them."
msgstr ""
"Si comparta un proyecto en modo lectura, la gente podrá acceder a las tareas desde su portal.\n"
"                    Estas personas podrán ver las tareas pero no podrán editarlas."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "Días laborables para asignar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "Días laborables para cerrar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "Horas laborables para asignar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "Horas laborables para cerrar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr "Horario de trabajo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "Horas laborables para asignar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "Horas laborables para cerrar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the projects contained in these stages as"
" well?"
msgstr ""
"¿También desea desarchivar todos los proyectos contenidos en estas etapas?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr "¿Desea desarchivar también todas las tareas de estas etapas?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Write a message..."
msgstr "Escribir un mensaje..."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr "Escribiendo"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "Anualmente"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "Años"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#, python-format
msgid ""
"You are not able to switch the company of this stage to %(company_name)s "
"since it currently includes projects associated with "
"%(project_company_name)s. Please ensure that this stage exclusively consists"
" of projects linked to %(company_name)s."
msgstr ""
"No puede cambiar la empresa de esta etapa a %(company_name)s ya que incluye "
"proyectos asociados con %(project_company_name)s. Asegúrese que esta etapa "
"solo consiste en los proyectos vinculados a %(company_name)s."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You can only set a personal stage on a private task."
msgstr "Solo se puede establecer una etapa personal en una tarea privada."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You can either archive them or"
" first delete all of their projects."
msgstr ""
"No puede eliminar etapas que contengan proyectos. Puede archivarlas o "
"eliminar todos sus proyectos primero."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You should first delete all of"
" their projects."
msgstr ""
"No puede eliminar etapas que contengan proyectos. Primero debe eliminar "
"todos sus proyectos."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"No puede eliminar etapas que contienen tareas. Puede archivarlos o eliminar "
"primero todas sus tareas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr ""
"No puede eliminar etapas que contienen tareas. Primero debe eliminar todas "
"sus tareas."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You cannot read %s fields in task."
msgstr "No puede leer los campos %s en la tarea."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You cannot write on %s fields in task."
msgstr "No puede escribir en los campos %s en la tarea."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Ha sido asignado a %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "Ha sido asignado/a a"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"You have full control and can revoke portal access anytime. Are you ready to"
" proceed?"
msgstr ""
"Usted tiene todo el control y puede revocar el permiso de acceso al portal "
"en cualquier momento, ¿está seguro de proceder?"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You have not write access of %s field."
msgstr "No tiene acceso de edición del campo %s."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "You must be"
msgstr "Usted debe ser"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Your managers decide which feedback is accepted"
msgstr "Sus gerentes deciden qué comentarios se aceptan"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "and"
msgstr "y"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr ""
"y cuál\n"
"      pasarán a la columna de \"Rechazado\"."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr "personas asignadas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "avatar"
msgstr "avatar"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "button."
msgstr "."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "comments"
msgstr "comentarios"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr "p. ej. calificación mensual"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "e.g. Office Party"
msgstr "p. ej. Fiesta en la oficina"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr "p. ej., lanzamiento de producto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr "p. ej. enviar invitaciones"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "e.g. Tasks"
msgstr "p. ej. tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_tree
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "e.g. To Do"
msgstr "p. ej. actividades pendientes"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. domain.com"
msgstr "p. ej. dominio.com"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "p. ej. fiesta de oficina"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr "p. ej. lanzamiento de producto"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon to organize your daily activities."
msgstr "para organizar sus actividades diarias."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"icon to see tasks waiting on other ones. Once a task is marked as complete "
"or canceled, all of its dependencies will be unblocked."
msgstr ""
"para ver las tareas que esperan a otras. Una vez que una tarea se marca como"
" completa o cancelada, se desbloquean todas sus dependencias."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon."
msgstr "."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "logged in"
msgstr "conectado"

#. module: project
#: model:ir.actions.server,name:project.action_server_view_my_task
msgid "menu view My Tasks"
msgstr "vista del menú Mis tareas"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "on a periodic basis"
msgstr "de forma periódica"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr "."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr "listo para ser marcado como alcanzado"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"state to indicate a request for changes or a need for discussion on a task."
msgstr ""
"para indicar una solicitud de cambio o que es necesario hablar sobre una "
"tarea."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"state to inform your colleagues that a task is approved for the next stage."
msgstr ""
"para informar a sus colegas que una tarea está aprobada para la siguiente "
"etapa."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "state to mark the task as canceled."
msgstr "para marcar la tarea como cancelada."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "state to mark the task as complete."
msgstr "para marcar la tarea como completada."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_name_with_subtask_count_char_field/project_task_name_with_subtask_count_char_field.xml:0
#, python-format
msgid "sub-tasks)"
msgstr "subtareas)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "task"
msgstr "tarea"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr "se ha actualizado la fecha límite del siguiente hito:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr "se ha actualizado la fecha límite de los siguientes hitos:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr ""
"para definir si el proyecto\n"
"      está listo para la siguiente etapa."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "to post a comment."
msgstr "para escribir un comentario."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "to signalize what is the current status of your Idea."
msgstr "para indicar cuál es el estado actual de su idea."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "when reaching a given stage"
msgstr "cuando llegue a una etapa específica"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr "generarán tareas en su proyecto"

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid ""
"{{ object.project_id.company_id.name or user.env.company.name }}: "
"Satisfaction Survey"
msgstr ""
"{{ object.project_id.company_id.name or user.env.company.name }}: Encuesta "
"de satisfacción"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid ""
"{{ record.closed_subtask_count.value }} sub-tasks closed out of {{ "
"record.subtask_count.value }}"
msgstr ""
"{{ record.closed_subtask_count.value }} subtareas cerradas de {{ "
"record.subtask_count.value }}"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
#, python-format
msgid "👤 Unassigned"
msgstr "👤 Sin asignar"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_graph/project_task_graph_model.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
#, python-format
msgid "🔒 Private"
msgstr "🔒 Privado"
