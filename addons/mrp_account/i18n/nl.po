# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
#
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil O<PERSON>o, 2025
# <PERSON><PERSON>, 2025
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-07 18:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$1000"
msgstr "$ 1000"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$5000"
msgstr "$ 5000"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/stock_move.py:0
#, python-format
msgid "%s - Unbuild Cost Difference"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "2023-08-15"
msgstr "15-08-2023"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Analytic Account</span>"
msgstr "<span class=\"o_stat_text\">Analytische rekening</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">Waardering</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span style=\"margin-right: 15px;\">Total</span>"
msgstr "<span style=\"margin-right: 15px;\">Totaal</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Amount</span>"
msgstr "<span>Bedrag</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Date</span>"
msgstr "<span>Datum</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Product</span>"
msgstr "<span>Product</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Quantity</span>"
msgstr "<span>Aantal</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Ref.</span>"
msgstr "<span>Ref.</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Unit of Measure</span>"
msgstr "<span>Maateenheid</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Acme Corp."
msgstr "Acme Corp."

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_account_ids
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_account_ids
msgid "Analytic Account"
msgstr "Analytische rekening"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#, python-format
msgid "Analytic Accounts"
msgstr "Analytische rekeningen"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_distribution
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_distribution
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analytisch verdeelmodel"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_distribution_search
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_distribution_search
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Zoek analytisch verdeelmodel"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_distribution_text
msgid "Analytic Distribution Text"
msgstr "Tekst analytisch verdeelmodel"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytische boeking"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Toepassingen van analytische dimensies"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_precision
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_precision
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_precision
msgid "Analytic Precision"
msgstr "Analytische nauwkeurigheid"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_bom
msgid "Bill of Material"
msgstr "Stuklijst"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Bills of Materials"
msgstr "Stuklijsten"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "Aantal stuklijsten"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
msgid "Bom"
msgstr "Stuklijst"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "Categorie"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "Bereken kostprijs vanuit stuklijst"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Bereken de prijs van het product gebruikmakend van producten en bewerkingen "
"gerelateerd aan de stuklijst, enkel voor geproduceerde producten."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_ids
msgid "Costs Hour Account"
msgstr "Uurkosten account"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Distributie analytisch rekening"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domein"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr "Extra eenheidkosten"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Laptop"
msgstr "Laptop"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Stuklijstoverzichtsrapport"

#. module: mrp_account
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_applicability__business_domain__manufacturing_order
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "Productieorder"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Manufacturing Orders"
msgstr "Productieorders"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
msgid "Manufacturing Orders Count"
msgstr "Aantal productieorders"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_ids
msgid "Mo Analytic Account Line"
msgstr "Analytische rekening productieorderregel"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr "Product"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_category
msgid "Product Category"
msgstr "Productcategorie"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr "Productvariant"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
msgid "Production"
msgstr "Productie"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid "Production Account"
msgstr "Productie rekening"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr "Productieorder"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "REF123"
msgstr "REF123"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "Toon waardering"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "Voorraadverplaatsing"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_rule
msgid "Stock Rule"
msgstr "Voorraadregel"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid ""
"This account will be used as a valuation counterpart for both components and "
"final products for manufacturing orders.\n"
"                If there are any workcenter/employee costs, this value will "
"remain on the account once the production is completed."
msgstr ""
"Deze rekening wordt gebruikt als tegenwaarde voor de waardering van zowel "
"componenten als eindproducten voor productieorders.\n"
"Als er werkplek-/personeelskosten zijn, blijft deze waarde op de rekening "
"staan ​​zodra de productie is voltooid."

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Units"
msgstr "Stuk(s)"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "WIP Report for"
msgstr "WIP rapportage voor"

#. module: mrp_account
#: model:ir.actions.report,name:mrp_account.wip_report
msgid "WIP report"
msgstr "WIP rapportage"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_ids
msgid "Wc Analytic Account Line"
msgstr "Analytische rekening werkplek"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "Werkplek"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Gebruik werkplek"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "Werkorder"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "Aantal werkorders"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#, python-format
msgid "Work Orders"
msgstr "Werkorders"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenter"
msgstr "Werkplek"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
#, python-format
msgid "[WC] %s"
msgstr "[WP] %s"

#~ msgid "Stock Valuation Layer"
#~ msgstr "Voorraadwaardelaag"
