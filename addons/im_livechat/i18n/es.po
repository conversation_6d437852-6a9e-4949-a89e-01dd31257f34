# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * im_livechat
#
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
#
# "<PERSON><PERSON> (lman)" <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2025-08-13 01:10+0000\n"
"Last-Translator: \"<PERSON><PERSON> (lman)\" <<EMAIL>>\n"
"Language-Team: Spanish <https://translate.odoo.com/projects/odoo-17/"
"im_livechat/es/>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n != 0 && n % 1000000 == 0)"
" ? 1 : 2);\n"
"X-Generator: Weblate 5.12.2\n"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#, python-format
msgid " (copy)"
msgstr " (copia)"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "\"%s\" is not a valid email."
msgstr "\"%s\" no es un correo electrónico válido."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "# Messages"
msgstr "N.° de mensajes"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "# Calificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "# de sesiones"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
#, fuzzy
msgid "# of speakers"
msgstr "N.° de participantes"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% Feliz"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "% de felicidad"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid ""
"%(new_line)sType %(bold_start)s:shortcut%(bold_end)s to insert a canned "
"response in your message."
msgstr ""
"%(new_line)sEscriba %(bold_start)s:acceso rápido%(bold_end)s para insertar "
"una respuesta predefinida en su mensaje."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%s has joined"
msgstr "%s se ha unido"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid ""
"'%(input_email)s' does not look like a valid email. Can you please try "
"again?"
msgstr ""
"'%(input_email)s' no parece ser un correo electrónico válido. ¿Podría volver"
" a intentarlo?"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Show' displays the chat button on the pages.\n"
"* 'Show with notification' is 'Show' in addition to a floating text just next to the button.\n"
"* 'Open automatically' displays the button and automatically opens the conversation pane.\n"
"* 'Hide' hides the chat button on the pages.\n"
msgstr ""
"* \"Mostrar\" muestra el botón de chat en todas las páginas.\n"
"* \"Mostrar con notificación\" es similar a \"Mostrar\" pero cuenta con un texto flotante junto al botón.\n"
"* \"Abrir automáticamente\" muestra el botón y abre automáticamente la ventana de conversación.\n"
"* \"Ocultar\" oculta el botón de chat en las páginas.\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ", en el"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "1 is Monday, 7 is Sunday"
msgstr "1 es lunes, 7 es domingo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "<i class=\"fa fa-mail-reply me-2\" title=\"Reply\"/>"
msgstr "<i class=\"fa fa-mail-reply me-2\" title=\"Reply\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Back to edit mode"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>Volver al modo de edición"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "<i title=\"Remove operator\" class=\"fa fa-fw fa-lg fa-close\"/>"
msgstr "<i title=\"Remove operator\" class=\"fa fa-fw fa-lg fa-close\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr ""
"<span style=\"font-size: 10px;\">Conversación de Chat en directo</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>Saludos,</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>Hola,</span><br/> aquí tiene una copia de su conversación con"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Reminder: This step will only be played if no operator is "
"available.</span>"
msgstr ""
"<span>Recordatorio: este paso solo sucederá si no hay operadores "
"disponibles.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before the Bot can perform more complex actions (Forward to an Operator, ...). </span>\n"
"                    <span>Use Channel Rules if you want the Bot to interact with visitors only when no operator is available.</span>"
msgstr ""
"<span>Consejo: para que el bot pueda realizar acciones complejas (enviar a un operador, etc.) es necesario que ocurra al menos una interacción (pregunta, correo electrónico, etc.). </span>\n"
"                    <span>Utilice las reglas de canal si desea que el bot interactúe con los visitantes solo cuando no hay operadores disponibles.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before "
"the Bot can perform more complex actions (Forward to an Operator, "
"...).</span>"
msgstr ""
"<span>Consejo: para que el bot pueda realizar acciones complejas (enviar a "
"un operador, etc.) es necesario que ocurra al menos una interacción "
"(pregunta, correo electrónico, etc.).</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Tip: Plan further steps for the Bot in case no operator is "
"available.</span>"
msgstr ""
"<span>Consejo: agregue los pasos adicionales que el bot debe seguir en caso "
"de que no haya ningún operador disponible.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<span>You are currently testing</span>"
msgstr "<span>Se están realizando pruebas</span>"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_chatbot_message__unique_mail_message_id
msgid "A mail.message can only be linked to a single chatbot message"
msgstr "Un mail.message solo se puede vincular a un solo mensaje de chatbot"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"Una sesión no tiene respuesta si el operador no respondió. \n"
"                                       Si el visitante es también el operador, la sesión siempre estará respondida."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__active
msgid "Active"
msgstr "Activo"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "Administrador"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "An error occurred. Please try again."
msgstr "Ocurrió un error. Intente nuevamente más tarde."

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_redirect
msgid "And tadaaaa here you go! 🌟"
msgstr "Y... ¡Listo! Aquí tiene 🌟"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Anonymous"
msgstr "Anónimo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Nombre anónimo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__name
msgid "Answer"
msgstr "Respuesta"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__answer_ids
msgid "Answers"
msgstr "Respuestas"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Archived"
msgstr "Archivado"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "¿Está dentro de la matrix?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__available_operator_ids
msgid "Available Operator"
msgstr "Operador disponible"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg
msgid "Average Rating"
msgstr "Calificación promedio"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Calificación promedio (%)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Duración promedio"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Mensaje promedio"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating"
msgstr "Calificación promedio"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating given by the visitor"
msgstr "Calificación promedio otorgada por el visitante"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr ""
"Tiempo promedio en segundos para dar la primera respuesta al visitante."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Tiempo promedio para proporcionar la primera respuesta al visitante"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Bad Ratings"
msgstr "Malas calificaciones"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__operator_partner_id
msgid "Bot Operator"
msgstr "Operador de bot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "Color del fondo del botón"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "Color del texto del botón"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Respuestas predefinidas"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Channel"
msgstr "Canal"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "Color del encabezado del canal"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel_member
msgid "Channel Member"
msgstr "Miembro del canal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Nombre del canal"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Regla del canal"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Reglas del canal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Tipo de canal"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Channels"
msgstr "Canales"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "Marcador de posición de entrada de texto"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"El chat es privado y único entre 2 personas. El grupo es privado entre las "
"personas invitadas. El acceso al canal es libre (dependiendo de su "
"configuración)."

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.chatbot_script_action
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_script_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Chatbot"
msgstr "Chatbot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_current_step_id
msgid "Chatbot Current Step"
msgstr "Paso actual del chatbot"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_message
msgid "Chatbot Message"
msgstr "Mensaje del chatbot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_message_ids
msgid "Chatbot Messages"
msgstr "Mensajes del chatbot"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Chatbot Name"
msgstr "Nombre del chatbot"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "Guion del chatbot"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_answer
msgid "Chatbot Script Answer"
msgstr "Respuesta de guion del chatbot"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "Paso de guion del chatbot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__script_step_id
msgid "Chatbot Step"
msgstr "Paso del chatbot"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.chatbot_config
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Chatbots"
msgstr "Chatbots"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "Close"
msgstr "Cerrar"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Close conversation"
msgstr "Cerrar conversación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Código"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Configuración"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Configure Channel"
msgstr "Configurar canal"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Conversación"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Conversation ended..."
msgstr "Conversación terminada..."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "Conversación con %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "Conversaciones gestionadas"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""
"Copie y pegue este código en la etiqueta &lt;head&gt; del HTML de su sitio "
"web:"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__country_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
msgid "Country"
msgstr "País"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "País del visitante"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "País del visitante del canal"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid "Create a Chatbot"
msgstr "Crear un chatbot"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "Cree un canal y comience a chatear para completar su historial."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Creado el"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "Fecha de creación"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Fecha de creación (hora)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Calificación de clientes"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "Número de día"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "Días de actividad"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "Color de fondo por defecto del botón de Chat en directo"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "Color de fondo por defecto del encabezado del canal una vez abierto"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "Color de texto por defecto del botón de Chat en directo"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr ""
"Texto por defecto mostrado en el botón de asistencia por chat en directo"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "Color del título por defecto del canal una vez abierto"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "Definir un nuevo canal de chat en el sitio web "

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Define rules for your live support channel. You can apply an action for the "
"given URL, and per country.<br/>To identify the country, GeoIP must be "
"installed on your server, otherwise, the countries of the rule will not be "
"taken into account."
msgstr ""
"Defina reglas para su canal de soporte en vivo. Puede aplicar una acción "
"para la URL dada y por país.<br/>Para identificar el país, GeoIP debe estar "
"instalado en su servidor, de lo contrario, los países de la regla no se "
"tomarán en cuenta."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Open automatically' otherwise this parameter will "
"not be taken into account."
msgstr ""
"Retraso (en segundos) para abrir automáticamente la ventana de conversación."
" Nota: la acción seleccionada debe ser \"Abrir automáticamente\", de lo "
"contrario este parámetro no se tendrá en cuenta."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Did we correctly answer your question?"
msgstr "¿Hemos respondido correctamente a su pregunta? "

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "Resumen"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/views/discuss_channel_list/discuss_channel_list_view_controller.js:0
#, python-format
msgid "Discuss"
msgstr "Conversaciones"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__discuss_channel_id
msgid "Discussion Channel"
msgstr "Canal de conversaciones"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_button.xml:0
#, python-format
msgid "Drag to Move"
msgstr "Arrastrar para mover"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__duration
msgid "Duration"
msgstr "Duración"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Duration of Session (min)"
msgstr "Duración de la sesión (min)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Duración de la conversación (en segundos)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__duration
msgid "Duration of the session in hours"
msgstr "Duración de la sesión en horas"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_email
msgid "Email"
msgstr "Correo electrónico"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
msgid "Enable the bot only if there is no operator available"
msgstr "Habilitar el bot solo si no hay operador disponible"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Enabled only if no operator"
msgstr "Habilitado solo si no hay un operador"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Explain your note"
msgstr "Explique su nota"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_invalid
msgid "First Step Invalid"
msgstr "Primer paso no válido"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_operator
msgid "First Step Operator"
msgstr "Operador de primer paso"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__first_step_warning
msgid "First Step Warning"
msgstr "Advertencia de primer paso"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""
"Para los sitios creados con el creador de sitios web de Odoo, vaya a Sitio "
"web &gt; Configuración &gt; Ajustes y seleccione el canal de chat en directo"
" del sitio web que desea añadir a su sitio web."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__forward_operator
msgid "Forward to Operator"
msgstr "Enviar al operador"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_single
msgid "Free Input"
msgstr "Entrada libre"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_multi
msgid "Free Input (Multi-Line)"
msgstr "Entrada libre (multilínea)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""
"Dada el orden de encontrar una regla que coincida. Si 2 reglas coinciden "
"para el país o URL dada, la regla con la menor secuencia será elegida."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Good Ratings"
msgstr "Buenas calificaciones"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Group By..."
msgstr "Agrupar por..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__has_access_livechat
msgid "Has access to Livechat"
msgstr "Tiene acceso al chat en directo"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
#, python-format
msgid "Have a Question? Chat with us."
msgstr "¿Tiene alguna pregunta? Chatee con nosotros."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "Color del fondo del encabezado"

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr "Hola, ¿cómo puedo ayudarle?"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide"
msgstr "Ocultar"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "History"
msgstr "Historial"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing
msgid ""
"Hmmm, let me check if I can find someone that could help you with that..."
msgstr ""
"Mmmm, déjeme revisar si puedo encontrar a alguien que pueda ayudarle con "
"eso..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "Hora de la fecha de inicio de sesión "

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#, python-format
msgid "How may I help you?"
msgstr "¿Cómo puedo ayudarle?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "¿Cómo usar el widget de Chat en directo del Sitio web?"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr "Oh, no, parece que ninguno de nuestros operadores está disponible 🙁"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_just_looking
msgid "I am just looking around"
msgstr "Sólo estoy mirando"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_documentation
msgid "I am looking for your documentation"
msgstr "Estoy buscando su documentación"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_pricing
msgid "I have a pricing question"
msgstr "Tengo una pregunta sobre los precios"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_exit
msgid "If you need anything else, feel free to get back in touch"
msgstr "Si necesita algo más, no dude en contactarnos"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1920
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
msgid "Image"
msgstr "Imagen"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1024
msgid "Image 1024"
msgstr "Imagen 1024"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_512
msgid "Image 512"
msgstr "Imagen 512"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__is_forward_operator_child
msgid "Is Forward Operator Child"
msgstr "Es hijo de transmisión del operador"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr "La categoría del chat en directo está abierta"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "¿Está el chat en marcha?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "¿El visitante es anónimo?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Unirse"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Unirse al canal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "Valor KPI de las conversaciones del chat en directo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "Valor de calificación de KPI del chat en directo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "Valor de respuesta de KPI en el chat en directo"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_invitation_patch.xml:0
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
#, python-format
msgid "Lang"
msgstr "Idioma"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Últimas 24 horas"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Abandonar"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Abandonar canal"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Chat en directo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Live Chat Button"
msgstr "Botón de chat en directo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "Búsqueda del canal de chat en directo"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/discuss_app_model_patch.js:0
#: code:addons/im_livechat/static/src/core/web/messaging_menu_patch.js:0
#: code:addons/im_livechat/static/src/core/web/thread_icon_patch.xml:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "Chat en directo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "Botón de chat en directo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "Color del botón de chat en directo"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Canal de chat en directo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__livechat_channel_count
msgid "Livechat Channel Count"
msgstr "Número de canales de chat en directo"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "Reglas del canal de chat en directo"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__discuss_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Conversación de chat en directo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.qunit_embed_suite
msgid "Livechat External Tests"
msgstr "Pruebas externas de chat en directo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_lang_ids
msgid "Livechat Languages"
msgstr "Idiomas del chat en directo"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_discuss_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "Se requiere el ID del operador del chat para un canal de tipo chat."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "Informe del canal de asistencia del chat en directo"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audience, the duration of a session, etc."
msgstr ""
"Las estadísticas del canal de asistencia del chat en directo le permiten "
"revisar y analizar con facilidad el rendimiento de la sesión en chat en "
"directo de su empresa. Obtenga información sobre las sesiones perdidas, el "
"público, la duración de una sesión, etc."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "Informe del operador de asistencia del chat en directo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Estadísticas de asistencia del chat en directo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_username
msgid "Livechat Username"
msgstr "Nombre de usuario del chat en directo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "Ventana del chat en directo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_lang_ids
msgid "Livechat languages"
msgstr "Idiomas del chat en directo"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__livechat_active
msgid "Livechat session is active until visitor leaves the conversation."
msgstr ""
"La sesión de chat en directo está activa hasta que el visitante deje la "
"conversación."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "Orden coincidente"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__message
msgid "Message"
msgstr "Mensaje"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Messages per session"
msgstr "Mensajes por sesión"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "Sesiones perdidas"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "My Sessions"
msgstr "Mis sesiones"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__name
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Name"
msgstr "Nombre"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr "No hay ningún colaborador disponible, vuelva a intentarlo más tarde."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr ""
"Aún no hay calificaciones de los clientes sobre la sesión de chat en directo"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "¡Todavía no hay información!!"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "No history found"
msgstr "No se encontró ningún historial"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Notification text"
msgstr "Texto de notificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__chatbot_script_count
msgid "Number of Chatbot"
msgstr "Número de chatbots"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
msgid "Number of conversation"
msgstr "Número de conversación"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "Número de días desde la primera sesión del operador"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Número de participantes diferentes"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Número de mensaje en la conversación"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Language"
msgstr "Idioma del chat en línea"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "Nombre de chat en línea"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Only If"
msgstr "Solo si"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Open automatically"
msgstr "Abrir automáticamente"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Open automatically timer"
msgstr "Abrir automáticamente el temporizador"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_operator_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Operador"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Análisis de operador"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Operadores"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Los operadores que no muestren ninguna actividad en Odoo durante más de 30 "
"minutos se considerarán desconectados."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_answer_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "Optional Link"
msgstr "Enlace opcional"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Opciones"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_author_name
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_author_name
msgid "Parent Author Name"
msgstr "Nombre del autor padre"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_body
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_body
msgid "Parent Body"
msgstr "Cuerpo padre"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Participant"
msgstr "Participante"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Participants"
msgstr "Participantes"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Porcentaje de calificaciones positivas"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_phone
msgid "Phone"
msgstr "Teléfono"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please call me on: "
msgstr "Por favor, llámeme al:"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please contact me on: "
msgstr "Por favor, contácteme por:"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_just_looking
msgid "Please do! If there is anything we can help with, let us know"
msgstr "Díganos si hay algo en lo que podamos ayudarle."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Con tecnología de"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_selection
msgid "Question"
msgstr "Pregunta"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
#, python-format
msgid "Rating"
msgstr "Calificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "Texto de calificación promedio"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Última retroalimentación de la calificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Última imagen de la calificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Último valor de la calificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Satisfacción de calificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_text
msgid "Rating Text"
msgstr "Texto de calificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_count
msgid "Rating count"
msgstr "Número de calificaciones"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Ratings"
msgstr "Calificaciones"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "Calificaciones para el canal de chat en directo"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "Receive a copy of this conversation."
msgstr "Reciba una copia de esta conversación."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__redirect_link
msgid "Redirect Link"
msgstr "Enlace de redirección"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""
"Expresión regular que especifica las páginas web en las que se aplicará esta"
" regla."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_message_id
msgid "Related Mail Message"
msgstr "Mensaje de correo relacionado"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Replied to"
msgstr "Respondido a"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Informe"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#, python-format
msgid "Reset to default colors"
msgstr "Restablecer a los colores por defecto"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/thread_actions.js:0
#, python-format
msgid "Restart Conversation"
msgstr "Reiniciar conversación"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Restarting conversation..."
msgstr "Reiniciando la conversación..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Reglas"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "Tasa de satisfacción"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "Guarde su canal para obtener su widget de configuración."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Say something"
msgstr "Diga algo"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/composer_patch.js:0
#, python-format
msgid "Say something..."
msgstr "Diga algo..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Script"
msgstr "Guion"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Guion (externo)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__script_step_id
msgid "Script Step"
msgstr "Paso del guion"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__script_step_ids
msgid "Script Steps"
msgstr "Pasos del guion"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Search history"
msgstr "Historial de búsqueda"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "Buscar informe"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_commands_patch.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "Ver últimas 15 páginas visitadas"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Select an option above"
msgstr "Seleccione una de las opciones anteriores"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Send"
msgstr "Enviar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__sequence
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Session Date"
msgstr "Fecha de la sesión"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Session Form"
msgstr "Formulario de sesión"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Estadísticas de sesión"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "Sesión no calificada"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "Sesiones sin respuesta"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Sesiones"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "Historial de sesiones"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Show"
msgstr "Mostrar"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Show this step only if all of these answers have been selected."
msgstr "Muestre este paso sólo si se han seleccionado todas estas respuestas."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button_and_text
msgid "Show with notification"
msgstr "Mostrar con notificación"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__source_id
msgid "Source"
msgstr "Origen"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Fecha de inicio de la sesión"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "Hora de inicio de la sesión"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "Tipo de paso"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/composer_patch.xml:0
#, python-format
msgid "Tab to next livechat"
msgstr "Pestaña al siguiente chat en directo"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__text
msgid "Text"
msgstr "Texto"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Texto del botón"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "Texto que solicita al usuario que inicie el chat."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Text to display on the notification"
msgstr "Texto que mostrar en la notificación"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Thank you for your feedback"
msgstr "Gracias por su retroalimentación"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "El canal de la regla"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "The conversation was sent."
msgstr "Se envió la conversación."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide', the "
"chat button will be hidden on the specified URL from the visitors located in"
" these 2 countries. This feature requires GeoIP installed on your server."
msgstr ""
"Esta regla solo se aplicará a estos países. Por ejemplo, si seleccionó "
"\"Bélgica\" y \"Estados Unidos\" y estableció la acción como \"Ocultar\", el"
" botón de chat se ocultará en la URL específica de los visitantes que se "
"ubican en estos 2 países. Esta función requiere que instale GeoIP en su "
"servidor."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "El usuario será capaz de eliminar los canales de asistencia."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "El usuario será capáz de unirse a los canales de asistencia."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_answer__redirect_link
msgid ""
"The visitor will be redirected to this link upon clicking the option (note "
"that the script will end if the link is external to the livechat website)."
msgstr ""
"Se redireccionará al visitante a este enlace cuando haga clic en la opción "
"(tome en cuenta que el guion terminará si el enlace es externo al sitio web "
"del chat en directo)."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "No hay una calificación para este canal en este momento"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_lang_ids
msgid ""
"These languages, in addition to your main language, will be used to assign "
"you to Live Chat sessions."
msgstr ""
"Estos son los idiomas que se usarán además de su idioma principal para "
"asignarle sesiones del chat en directo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "This Month"
msgstr "Este mes"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "Esta semana"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""
"Este es un mensaje de bienvenida automático que su visitante verá al iniciar"
" una nueva conversación."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr ""
"Este nombre de usuario se usará como su nombre en los canales de chat en "
"directo."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "Tiempo para responder "

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "Tiempo para responder (seg)"

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "Consejo: utiliza respuestas predefinidas para chatear más rápido"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__title
msgid "Title"
msgstr "Título"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "Color del título"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "Sesiones tratadas"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "Expresión regular de URL"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"URL a una página estática donde su cliente podrá conversar con el operador "
"del canal."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Unrated"
msgstr "Sin calificación"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""
"Utilice respuestas predefinidas para establecer plantillas de mensajes en la"
" aplicación Chat en directo. Para cargar una respuesta predefinida, empiece "
"su oración con \":\" y seleccione la plantilla."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "Usuario"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "El nombre de usuario del chat en directo"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "Ajustes de usuario"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_script_answer_id
msgid "User's answer"
msgstr "Respuesta del usuario"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_raw_answer
msgid "User's raw answer"
msgstr "Respuesta bruta del usuario"

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
#, python-format
msgid "Visitor"
msgstr "Visitante"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "El visitante está feliz"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Visitor left the conversation."
msgstr "El visitante ha abandonado la conversación."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Página web"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Canales de chat en directo del sitio web"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: im_livechat
#: model:chatbot.script,title:im_livechat.chatbot_script_welcome_bot
msgid "Welcome Bot"
msgstr "Bot de bienvenida"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Mensaje de bienvenida"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_welcome
msgid "Welcome to CompanyName! 👋"
msgstr "¡Bienvenido a CompanyName! 👋"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_dispatch
msgid "What are you looking for?"
msgstr "¿Qué está buscando?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "Widget"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr ""
"¿Podría proporcionarnos su dirección de correo electrónico para que podamos "
"contactarle?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "You"
msgstr "Usted"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload attachments on this channel."
msgstr "No tiene permitido subir archivos adjuntos en este canal."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid ""
"You can create a new Chatbot with a defined script to speak to your website "
"visitors."
msgstr ""
"Puede crear un nuevo chatbot con un guion definido para hablar con los "
"visitantes de su sitio web."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"Puede crear canales para cada sitio web en el que desee\n"
"                integrar el widget de chat en directo del sitio web, lo que permite a los visitantes\n"
"                de su sitio web hablar en tiempo real con sus operadores."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Your chatter history is empty"
msgstr "Su historial de chatter está vacío"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
#, python-format
msgid "country"
msgstr "país"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "e.g. \"Meeting Scheduler Bot\""
msgstr "p. ej. \"Bot para programar reuniones\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "e.g. 'How can I help you?'"
msgstr "p. ej. \"¿Cómo puedo ayudarle?\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "p. ej. /contactus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "p. ej. Hola, ¿cómo puedo ayudarle?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "p. ej. TuSitioWeb.com"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""
"o copie esta URL y envíela por correo electrónico a sus clientes o "
"proveedores:"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "segundos"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{author_name}}"
