# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#, python-format
msgid " (copy)"
msgstr " (副本)"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "\"%s\" is not a valid email."
msgstr "%s 不是有效的電子郵件。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "# Messages"
msgstr "# 訊息"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "評分數目"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "# 聊天視窗"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "講解人"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% 高興"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "% 滿意度"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid ""
"%(new_line)sType %(bold_start)s:shortcut%(bold_end)s to insert a canned "
"response in your message."
msgstr "%(new_line)s輸入 %(bold_start)s:shortcut%(bold_end)s 以插入預設回應至你的訊息中。"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%s has joined"
msgstr "%s 已經加入"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid ""
"'%(input_email)s' does not look like a valid email. Can you please try "
"again?"
msgstr "%(input_email)s 看起來不像是有效的電子郵件。你能再試一次嗎？"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Show' displays the chat button on the pages.\n"
"* 'Show with notification' is 'Show' in addition to a floating text just next to the button.\n"
"* 'Open automatically' displays the button and automatically opens the conversation pane.\n"
"* 'Hide' hides the chat button on the pages.\n"
msgstr ""
"* 「顯示」，在頁面上顯示聊天按鈕。\n"
"* 「顯示通知」，「顯示」加按鈕旁的浮動文本。\n"
"* 「自動打開」，顯示按鈕，並自動打開對話窗。\n"
"* 「隱藏」，隱藏頁面上的聊天按鈕。\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ",在"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "1 is Monday, 7 is Sunday"
msgstr "1 = 星期一，7 = 星期日"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "<i class=\"fa fa-mail-reply me-2\" title=\"Reply\"/>"
msgstr "<i class=\"fa fa-mail-reply me-2\" title=\"回覆\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"滿意評分百分比\" role=\"img\" aria-"
"label=\"開心笑臉\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Back to edit mode"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/> 返回編輯模式"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "<i title=\"Remove operator\" class=\"fa fa-fw fa-lg fa-close\"/>"
msgstr "<i title=\"移除操作員\" class=\"fa fa-fw fa-lg fa-close\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr "<span style=\"font-size: 10px;\">線上客服對話串</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>順祝 安康</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>你好！</span><br/>以下是你與我們先前對話的副本。對話操作員："

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Reminder: This step will only be played if no operator is "
"available.</span>"
msgstr "<span>提醒：只在沒有可用的運算符時，才會播放此步驟。</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before the Bot can perform more complex actions (Forward to an Operator, ...). </span>\n"
"                    <span>Use Channel Rules if you want the Bot to interact with visitors only when no operator is available.</span>"
msgstr ""
"<span>提示：至少需要一次互動（問題、電子郵件等），機器人才能執行更複雜的操作（轉發給操作員等）。 </span>\n"
"                    <span>如果您希望機器人僅在沒有操作員可用時與訪客互動，請使用通道規則。</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before "
"the Bot can perform more complex actions (Forward to an Operator, "
"...).</span>"
msgstr "<span>提示：至少需要一次互動（問題、電子郵件等），機器人才能執行更複雜的操作（轉發給操作員等）。</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Tip: Plan further steps for the Bot in case no operator is "
"available.</span>"
msgstr "<span>提示：在沒有操作員可用的情況下，為機器人計劃進一步的步驟。</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<span>You are currently testing</span>"
msgstr "<span>您當前正在測試</span>"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_chatbot_message__unique_mail_message_id
msgid "A mail.message can only be linked to a single chatbot message"
msgstr "郵件消息只能連結至單個聊天機器人訊息"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"如果操作員沒有應答，會話將無人應答。\n"
"                                       如果訪問者也是操作員，則會話將始終得到應答。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__active
msgid "Active"
msgstr "啟用"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "管理員"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "An error occurred. Please try again."
msgstr "發生錯誤。請重試。"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_redirect
msgid "And tadaaaa here you go! 🌟"
msgstr "完成!🌟"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Anonymous"
msgstr "匿名"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__anonymous_name
msgid "Anonymous Name"
msgstr "匿名使用者姓名"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__name
msgid "Answer"
msgstr "答案"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__answer_ids
msgid "Answers"
msgstr "答案"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Archived"
msgstr "已封存"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "您是否在矩陣中？"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__available_operator_ids
msgid "Available Operator"
msgstr "可用操作員"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Avatar"
msgstr "頭像"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg
msgid "Average Rating"
msgstr "平均評分"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "平均評分（%）"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "平均時長"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "平均消息"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating"
msgstr "平均評分"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating given by the visitor"
msgstr "訪客的平均評分"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr "首度回應訪客的平均時間，以秒計"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "首次回覆訪客的平均時間"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Bad Ratings"
msgstr "劣評"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__operator_partner_id
msgid "Bot Operator"
msgstr "機器人操作員"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "按鈕背景顏色"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "按鈕文字顏色"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "預設回應"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Channel"
msgstr "群組"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "頻道標題顏色"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel_member
msgid "Channel Member"
msgstr "渠道成員"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "頻道名稱"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "頻道規則"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "頻道規則"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "群組類型"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Channels"
msgstr "頻道"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "聊天輸入預留位置"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr "2 人之間的聊天是私密且獨特的。群組在受邀者之間是私人的。頻道可以自由加入（取決於其配置）。"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.chatbot_script_action
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_script_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Chatbot"
msgstr "聊天機器人"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_current_step_id
msgid "Chatbot Current Step"
msgstr "聊天機器人目前步驟"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_message
msgid "Chatbot Message"
msgstr "聊天機器人訊息"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_message_ids
msgid "Chatbot Messages"
msgstr "聊天機器人訊息"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Chatbot Name"
msgstr "聊天機器人名稱"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "聊天機器人程式碼"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_answer
msgid "Chatbot Script Answer"
msgstr "聊天機器人腳本答案"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "聊天機器人程式碼步驟"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__script_step_id
msgid "Chatbot Step"
msgstr "聊天機器人步驟"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.chatbot_config
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Chatbots"
msgstr "聊天機器人"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "Close"
msgstr "關閉"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Close conversation"
msgstr "關閉對話"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "程式碼"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "配置"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Configure Channel"
msgstr "配置頻道"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "對話"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Conversation ended..."
msgstr "對話已結束⋯"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "使用 %s 的對話"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "對話已處理"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr "複製以下程式碼，貼上至你的網站的 &lt;head&gt; 標籤內："

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__country_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
msgid "Country"
msgstr "國家"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "遊客所在國家/地區"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "此頻道訪客的國家/地區"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid "Create a Chatbot"
msgstr "創建聊天機器人"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "建立一個頻道，開始聊天就可以生成交談記錄。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "建立於"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "建立日期"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "建立日期(小時)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "客戶評級"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "日子數字"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "活動天數"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "即時聊天按鈕的預設背景顏色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "開啟後，頻道標題的預設背景顏色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "即時聊天按鈕的預設文字顏色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "線上客服按鈕上顯示的預設內容"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "開啟後，頻道的預設標題顏色"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "定義一個新的線上客服頻道網站"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Define rules for your live support channel. You can apply an action for the "
"given URL, and per country.<br/>To identify the country, GeoIP must be "
"installed on your server, otherwise, the countries of the rule will not be "
"taken into account."
msgstr ""
"為您的即時支持頻道定義規則。您可以對給定的網址和每個國家/地區應用操作。<br/>要識別國家/地區，必須在服務器上安裝 "
"GeoIP，否則將不考慮規則中的國家/地區。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Open automatically' otherwise this parameter will "
"not be taken into account."
msgstr "自動打開對話窗口的延遲，以秒計。請注意：所選操作須為「自動打開」，否則將不會考慮此參數。"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Did we correctly answer your question?"
msgstr "我們是否正確回答了您的問題？"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "摘要"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/views/discuss_channel_list/discuss_channel_list_view_controller.js:0
#, python-format
msgid "Discuss"
msgstr "討論"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__discuss_channel_id
msgid "Discussion Channel"
msgstr "討論群組"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_button.xml:0
#, python-format
msgid "Drag to Move"
msgstr "拖曳以移動"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__duration
msgid "Duration"
msgstr "時長"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Duration of Session (min)"
msgstr "對話時段時長（分鐘）"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "對話持續時間(秒)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__duration
msgid "Duration of the session in hours"
msgstr "對話時段時長（小時）"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_email
msgid "Email"
msgstr "電郵"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
msgid "Enable the bot only if there is no operator available"
msgstr "只在沒有可用的操作員時才啟用機器人"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Enabled only if no operator"
msgstr "只在沒有操作員時啟用"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Explain your note"
msgstr "解釋您的備註"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_invalid
msgid "First Step Invalid"
msgstr "第一步無效"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_operator
msgid "First Step Operator"
msgstr "第一步運算符"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__first_step_warning
msgid "First Step Warning"
msgstr "第一步警告"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr "對於用Odoo CMS建立的網站，進入網站 &gt; 配置 &gt; 設定，選擇您想添加到網站的網站即時聊天頻道。"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__forward_operator
msgid "Forward to Operator"
msgstr "轉發給操作員"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_single
msgid "Free Input"
msgstr "自由輸入"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_multi
msgid "Free Input (Multi-Line)"
msgstr "自由輸入（多行）"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr "幫助訂單找到匹配規則。如果2條規則同時匹配給定的網址l/國家，會選擇低序列的一個。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Good Ratings"
msgstr "好評"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Group By..."
msgstr "分組依據⋯"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__has_access_livechat
msgid "Has access to Livechat"
msgstr "可存取即時聊天"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
#, python-format
msgid "Have a Question? Chat with us."
msgstr "有問題？請與我們聯絡。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "頁首背景顏色"

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr "你好！我可以如何幫助你？"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide"
msgstr "隱藏"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "History"
msgstr "歷史"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing
msgid ""
"Hmmm, let me check if I can find someone that could help you with that..."
msgstr "嗯，讓我看看有沒有人可以幫助你⋯"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "對話視窗開始的小時數"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#, python-format
msgid "How may I help you?"
msgstr "請問有什麼可以幫到您？"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "如何使用網站線上交談掛件?"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr "哎唷，目前似乎沒有服務員有空 🙁"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_just_looking
msgid "I am just looking around"
msgstr "我只是周圍看看"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_documentation
msgid "I am looking for your documentation"
msgstr "我正在尋找您的文檔"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_pricing
msgid "I have a pricing question"
msgstr "我有一個關於定價問題"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "識別號"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_exit
msgid "If you need anything else, feel free to get back in touch"
msgstr "如果您需要其他任何內容，請隨時與我們聯絡"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1920
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
msgid "Image"
msgstr "圖片"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1024
msgid "Image 1024"
msgstr "圖像 1024"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_128
msgid "Image 128"
msgstr "圖像 128"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_256
msgid "Image 256"
msgstr "圖像 256"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_512
msgid "Image 512"
msgstr "圖像 512"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__is_forward_operator_child
msgid "Is Forward Operator Child"
msgstr "是轉發操作員的子項"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr "類別實時聊天是否開放"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "線上討論是否正在進行？"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "訪客是否匿名"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "加入"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "加入頻道"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "KPI 實時聊天對話值"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "KPI 實時聊天評價值"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "KPI 實時聊天回應值"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_invitation_patch.xml:0
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
#, python-format
msgid "Lang"
msgstr "語言"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "過去24小時"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "退出"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "離開頻道"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "線上客服"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Live Chat Button"
msgstr "實時聊天按鈕"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "實時聊天頻道搜尋"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/discuss_app_model_patch.js:0
#: code:addons/im_livechat/static/src/core/web/messaging_menu_patch.js:0
#: code:addons/im_livechat/static/src/core/web/thread_icon_patch.xml:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "線上客服"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "實時聊天按鈕"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "實時聊天按鈕顏色"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "線上客服頻道"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__livechat_channel_count
msgid "Livechat Channel Count"
msgstr "實時聊天頻道數目"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "線上客服頻道規則"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__discuss_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "線上對話"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.qunit_embed_suite
msgid "Livechat External Tests"
msgstr "實時聊天外部測試"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_lang_ids
msgid "Livechat Languages"
msgstr "實時聊天語言"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_discuss_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "實時聊天類型的頻道需要有實時聊天操作員識別碼。"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "線上客服支援頻道報告"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audience, the duration of a session, etc."
msgstr "實時聊天支援頻道統計可讓您輕鬆檢查和分析公司實時聊天對話運作表現。 提取有關錯過的對話時段、觀眾、對話持續時間等的資訊。"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "線上客服支援客服人員報告"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "線上客服支援統計"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_username
msgid "Livechat Username"
msgstr "實時聊天用戶名"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "實時聊天視窗"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_lang_ids
msgid "Livechat languages"
msgstr "實時聊天語言"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__livechat_active
msgid "Livechat session is active until visitor leaves the conversation."
msgstr "實時聊天對話時段會一直生效，直至訪客離開對話。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "匹配的訂單"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__message
msgid "Message"
msgstr "消息"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_ids
msgid "Messages"
msgstr "訊息"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Messages per session"
msgstr "每次對話時段的訊息"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "錯過的對話視窗"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "My Sessions"
msgstr "我的營業點"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__name
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Name"
msgstr "名稱"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr "沒有可用的協作者，請稍後再試。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "在線上客服對話視窗中還沒有客戶評分"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "暫無資料！"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "No history found"
msgstr "未找到歷史記錄"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Notification text"
msgstr "通知文字"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__chatbot_script_count
msgid "Number of Chatbot"
msgstr "聊天機器人數量"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
msgid "Number of conversation"
msgstr "對話次數"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "自操作員的第一次對話以來的天數"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "其他發言人數"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "對話中的消息數量"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Language"
msgstr "線上聊天語言"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "線上聊天名稱"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Only If"
msgstr "只在"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Open automatically"
msgstr "自動開啟"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Open automatically timer"
msgstr "自動開啟計時器"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_operator_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "客服人員"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "客服人員分析"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "客服人員"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr "操作員若超過 30 分鐘未有在 Odoo 內顯示任何活動，將被視為已斷線。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_answer_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "Optional Link"
msgstr "可選連結"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "選項"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_author_name
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_author_name
msgid "Parent Author Name"
msgstr "母項作者姓名"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_body
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_body
msgid "Parent Body"
msgstr "母項內文"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Participant"
msgstr "參與者"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Participants"
msgstr "訊息參與者"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "滿意評分百分比"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_phone
msgid "Phone"
msgstr "電話"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please call me on: "
msgstr "請給我打電話："

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please contact me on: "
msgstr "請通過以下方式與我聯絡："

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_just_looking
msgid "Please do! If there is anything we can help with, let us know"
msgstr "當然可以! 如果有什麼我們可以提供幫助的，請告訴我們"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "官方技術支援"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_selection
msgid "Question"
msgstr "提問"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
#, python-format
msgid "Rating"
msgstr "評分"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "平均評分文字"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "最新回饋評分"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_image
msgid "Rating Last Image"
msgstr "最新評分圖像"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_value
msgid "Rating Last Value"
msgstr "最新評分值"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "評級滿意度"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_text
msgid "Rating Text"
msgstr "評分文字"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_count
msgid "Rating count"
msgstr "評分數"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Ratings"
msgstr "評分"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "線上客服頻道的評價"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "Receive a copy of this conversation."
msgstr "接收此對話的副本。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__redirect_link
msgid "Redirect Link"
msgstr "重新導向連結"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr "指定此規則將在網頁上應用的正則表達式。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_message_id
msgid "Related Mail Message"
msgstr "相關郵件訊息"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Replied to"
msgstr "回覆給"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "報表"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#, python-format
msgid "Reset to default colors"
msgstr "重設為預設顏色"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/thread_actions.js:0
#, python-format
msgid "Restart Conversation"
msgstr "重新開始對話"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Restarting conversation..."
msgstr "正在重新開始對話⋯"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "規則"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "滿意率"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "儲存您的頻道以獲得您的配置小工具。"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Say something"
msgstr "說些東西⋯"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/composer_patch.js:0
#, python-format
msgid "Say something..."
msgstr "說些東西.."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Script"
msgstr "腳本"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "腳本（外部）"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__script_step_id
msgid "Script Step"
msgstr "腳本步驟"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__script_step_ids
msgid "Script Steps"
msgstr "腳本步驟"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Search history"
msgstr "搜尋歷史"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "搜尋報表"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_commands_patch.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "查看最近訪問的 15 個頁面"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Select an option above"
msgstr "選擇上方一個選項"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Send"
msgstr "發送"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__sequence
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__sequence
msgid "Sequence"
msgstr "序列號"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Session Date"
msgstr "對話視窗日期"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Session Form"
msgstr "對話視窗表格"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "對話統計"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "未評分對話"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "沒有回應的對話"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "對話視窗"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "對話視窗歷程"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Show"
msgstr "顯示"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Show this step only if all of these answers have been selected."
msgstr "只在選擇了所有這些答案時，才顯示此步驟。"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button_and_text
msgid "Show with notification"
msgstr "顯示通知"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__source_id
msgid "Source"
msgstr "來源"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "對話視窗的開始日期"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "對話時段開始時間"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "步驟類型"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/composer_patch.xml:0
#, python-format
msgid "Tab to next livechat"
msgstr "Tab to next livechat"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__text
msgid "Text"
msgstr "文字"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "按鈕的文本"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "提示使用者發起聊天的文本。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Text to display on the notification"
msgstr "通知上要顯示的文字"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Thank you for your feedback"
msgstr "感謝您對本次客服的評分回饋"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "規則的頻道"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "The conversation was sent."
msgstr "對話已發送。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide', the "
"chat button will be hidden on the specified URL from the visitors located in"
" these 2 countries. This feature requires GeoIP installed on your server."
msgstr ""
"本規則僅適用於這些國家。示例：如果您選擇「比利時」和「美國」，並將操作設置為「隱藏」，則聊天按鈕將對這兩個國家的訪問者、在指定網址上隱藏。本功能要求在服務器上安裝GeoIP。"

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "允許使用者刪除服務支援頻道。"

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "使用者能夠加入支援的頻道。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_answer__redirect_link
msgid ""
"The visitor will be redirected to this link upon clicking the option (note "
"that the script will end if the link is external to the livechat website)."
msgstr "按下該選項後，訪問者將被重新導向至此連結（請注意，如果連結位於實時聊天網站外部，腳本將結束）。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "這個頻道目前沒有評分"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_lang_ids
msgid ""
"These languages, in addition to your main language, will be used to assign "
"you to Live Chat sessions."
msgstr "除了您的主要語言外，這些語言將用於為您分配實時聊天對話時段。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "This Month"
msgstr "本月"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "本周"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr "這是一個自動的『歡迎』訊息，當初始化新的對話窗口時訪問者會看到。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr "這個用戶名將被用作你在實時聊天頻道中的名字。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "是時候回答了"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "回應時間（秒）"

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "提示：使用預設回覆，聊天速度更快"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__title
msgid "Title"
msgstr "稱謂"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "標題顏色"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "已處理的對話時段"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "網址的正則表達式"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr "您的客戶可以在靜態網頁的網址和頻道操作者進行討論。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Unrated"
msgstr "未評分"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr "使用預製回覆來定義實時聊天應用程式中的訊息範本。要載入預設回覆，請以「:」開始您的句子並選擇範本。"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "使用者"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "用戶實時聊天用戶名"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "使用者設定"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_script_answer_id
msgid "User's answer"
msgstr "用戶回答"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_raw_answer
msgid "User's raw answer"
msgstr "用戶的原始答案"

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
#, python-format
msgid "Visitor"
msgstr "訪客"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "訪客很滿意"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Visitor left the conversation."
msgstr "訪客已離開對話。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "網頁"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "網站線上客服頻道"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: im_livechat
#: model:chatbot.script,title:im_livechat.chatbot_script_welcome_bot
msgid "Welcome Bot"
msgstr "歡迎機器人"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "歡迎訊息"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_welcome
msgid "Welcome to CompanyName! 👋"
msgstr "歡迎來到 CompanyName！ 👋"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_dispatch
msgid "What are you looking for?"
msgstr "你在找什麼？"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "小工具"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr "介意留下你的電郵地址，以便我們回覆你嗎？"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "You"
msgstr "你"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload attachments on this channel."
msgstr "本頻道不允許上載附件。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid ""
"You can create a new Chatbot with a defined script to speak to your website "
"visitors."
msgstr "您可以使用定義的腳本創建一個新的聊天機器人，以與您的網站訪問者交談。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"您可以為每個想要\n"
" 整合網站線上客服小工具的網站建立頻道，這樣您的網站訪客便能夠與操作人員及時交流。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Your chatter history is empty"
msgstr "您的聊天歷史是空的"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
#, python-format
msgid "country"
msgstr "國家/地區"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "e.g. \"Meeting Scheduler Bot\""
msgstr "例如\"會議安排程序機器人\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "e.g. 'How can I help you?'"
msgstr "例如\"我能幫你什麼？\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "例：/contactus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "例如：您好！請問有什麼可以協助您的嗎？"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "例如：您的網站地址 "

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr "或者拷貝此網址並且通過信件發送給您的客戶、供應商："

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "秒"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{author_name}}"
