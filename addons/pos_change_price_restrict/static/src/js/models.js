/** @odoo-module */

import { Order } from "@point_of_sale/app/store/models";
import { patch } from "@web/core/utils/patch";

patch(Order, {
    setup() {
        super.setup();
    },
});

patch(Order.prototype, {
    async verifyPriceChangePin(pin) {
        try {
            const result = await this.env.services.rpc({
                model: 'res.users',
                method: 'verify_price_change_pin',
                args: [[this.pos.user.id], pin],
            });
            return result;
        } catch (error) {
            return false;
        }
    }
});
