/** @odoo-module */

import { ProductScreen } from "@point_of_sale/app/screens/product_screen/product_screen";
import { patch } from "@web/core/utils/patch";
import { ErrorPopup } from "@point_of_sale/app/errors/popups/error_popup";
import { NumberPopup } from "@point_of_sale/app/utils/input_popups/number_popup";
import { usePos } from "@point_of_sale/app/store/pos_hook";
import { _t } from "@web/core/l10n/translation";

patch(ProductScreen.prototype, {
    setup() {
        super.setup();
        this.pos = usePos();
    },

    async verifyPriceChangePermission() {
        const { confirmed, payload: pin } = await this.popup.add(NumberPopup, {
            title: _t('Enter Price Change PIN'),
            startingValue: '',
            isPassword: true,
        });

        if (!confirmed) {
            return false;
        }

        const order = this.pos.get_order();
        const isValid = await order.verifyPriceChangePin(pin);
        if (!isValid) {
            await this.popup.add(ErrorPopup, {
                title: _t('Invalid PIN'),
                body: _t('The entered PIN is incorrect. Price change is not allowed.'),
            });
            return false;
        }
        return true;
    },

    async _onClickProduct(event) {
        if (event.detail === 'price') {
            const product = event.target.product;
            if (await this.verifyPriceChangePermission()) {
                await this._showEditPricePopup(product);
            }
            return;
        }
        return super._onClickProduct(event);
    },

    async updateSelectedOrderline({ buffer, key }) {
        const order = this.pos.get_order();
        const selectedLine = order.get_selected_orderline();

        // Handle price change mode with PIN verification
        if (selectedLine && this.pos.numpadMode === "price") {
            if (!(await this.verifyPriceChangePermission())) {
                this.numberBuffer.reset();
                return;
            }
        }

        // Handle tip lines
        if (selectedLine && selectedLine.isTipLine() && this.pos.numpadMode !== "price") {
            this.numberBuffer.reset();
            if (key === "Backspace") {
                this._setValue("remove");
            } else {
                await this.popup.add(ErrorPopup, {
                    title: _t("Cannot modify a tip"),
                    body: _t("Customer tips cannot be modified directly"),
                });
            }
            return;
        }

        // Handle quantity restrictions
        if (selectedLine && 
            this.pos.numpadMode === "quantity" && 
            this.pos.disallowLineQuantityChange()) {
            const orderlines = order.orderlines;
            const lastId = orderlines.length !== 0 && orderlines.at(orderlines.length - 1).cid;
            const currentQuantity = selectedLine.get_quantity();

            if (selectedLine.noDecrease) {
                await this.popup.add(ErrorPopup, {
                    title: _t("Invalid action"),
                    body: _t("You are not allowed to change this quantity"),
                });
                return;
            }
            await this.handleOrderLineQuantityChange(selectedLine, buffer, currentQuantity, lastId);
            return;
        }

        // Handle discount restrictions
        if (selectedLine && 
            this.pos.numpadMode === "discount" && 
            this.pos.disallowLineDiscountChange()) {
            this.numberBuffer.reset();
            const { confirmed, payload: inputNumber } = await this.popup.add(NumberPopup, {
                startingValue: 10,
                title: _t("Set the new discount"),
                isInputSelected: true,
            });
            if (confirmed) {
                await this.pos.setDiscountFromUI(selectedLine, inputNumber);
            }
            return;
        }

        // Handle normal value setting
        const val = buffer === null ? "remove" : buffer;
        this._setValue(val);
        if (val === "remove") {
            this.numberBuffer.reset();
            this.pos.numpadMode = "quantity";
        }
    },

    async _showEditPricePopup(product) {
        const { confirmed, payload } = await this.popup.add(NumberPopup, {
            title: _t('Change Price'),
            startingValue: this.env.utils.formatCurrency(product.price),
        });

        if (confirmed) {
            await this._changeProductPrice(product, payload);
        }
    },
});
