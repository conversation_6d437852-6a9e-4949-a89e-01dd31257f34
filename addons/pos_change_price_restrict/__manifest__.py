{
    'name': 'POS Change Price Restriction',
    'version': '1.0',
    'category': 'Point of Sale',
    'summary': 'Restrict price changes in POS with PIN',
    'description': """
        This module adds PIN protection for price changes in Point of Sale:
        * Adds PIN field for users
        * Requires PIN verification before price changes
        * Blocks price changes on wrong PIN
    """,
    'depends': ['point_of_sale'],
    'data': [
        'views/res_users_views.xml',
        'security/ir.model.access.csv',
    ],
    'data': [
        'views/res_users_views.xml',
        'security/ir.model.access.csv',
    ],
    'assets': {
        'point_of_sale._assets_pos': [
            'pos_change_price_restrict/static/src/js/models.js',
            'pos_change_price_restrict/static/src/js/Screens/ProductScreen.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
