from odoo import fields, models, api

class ResUsers(models.Model):
    _inherit = 'res.users'

    pos_price_change_pin = fields.Char(string='POS Price Change PIN', 
                                     help='PIN required to change prices in POS',
                                     copy=False)

    @api.model
    def verify_price_change_pin(self, pin):
        """Verify the PIN for price changes"""
        if self.pos_price_change_pin and self.pos_price_change_pin == pin:
            return True
        return False
